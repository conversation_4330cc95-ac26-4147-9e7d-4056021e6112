/**
 * visual-script.module.ts
 * 
 * 视觉脚本服务模块
 */

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { VisualScriptController } from './visual-script.controller';
import { VisualScriptService } from './visual-script.service';
import { VersionController } from './version.controller';
import { VersionService } from './version.service';
import { CollaborationController } from './collaboration/collaboration.controller';
import { CollaborationService } from './collaboration/collaboration.service';
import { TemplateController } from './template.controller';
import { TemplateService } from './template.service';
import { VisualScript } from './entities/visual-script.entity';
import { ScriptVersion } from './entities/script-version.entity';
import { ScriptExecution } from './entities/script-execution.entity';
import { ScriptCollaborator } from './entities/script-collaborator.entity';
import { ScriptTemplate } from './entities/script-template.entity';
import { ScriptComment } from './entities/script-comment.entity';
import { ScriptTag } from './entities/script-tag.entity';

@Module({
  imports: [
    // 数据库实体
    TypeOrmModule.forFeature([
      VisualScript,
      ScriptVersion,
      ScriptExecution,
      ScriptCollaborator,
      ScriptTemplate,
      ScriptComment,
      ScriptTag
    ]),
    
    // 队列系统
    BullModule.registerQueue({
      name: 'script-execution',
    }),
    
    BullModule.registerQueue({
      name: 'script-validation',
    }),
    
    BullModule.registerQueue({
      name: 'script-optimization',
    })
  ],
  controllers: [
    VisualScriptController,
    VersionController,
    CollaborationController,
    TemplateController
  ],
  providers: [
    VisualScriptService,
    VersionService,
    CollaborationService,
    TemplateService
  ],
  exports: [
    VisualScriptService,
    VersionService,
    CollaborationService,
    TemplateService
  ]
})
export class VisualScriptModule {}
