import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { WebSocketService } from './websocket.service';

@WebSocketGateway({
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
    credentials: true,
  },
  namespace: '/ui',
})
export class UIWebSocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(UIWebSocketGateway.name);

  constructor(private readonly websocketService: WebSocketService) {}

  /**
   * 客户端连接
   */
  handleConnection(client: Socket) {
    this.logger.log(`客户端连接: ${client.id}`);
    this.websocketService.addClient(client);
  }

  /**
   * 客户端断开连接
   */
  handleDisconnect(client: Socket) {
    this.logger.log(`客户端断开连接: ${client.id}`);
    this.websocketService.removeClient(client.id);
  }

  /**
   * 加入房间
   */
  @SubscribeMessage('join-room')
  handleJoinRoom(
    @MessageBody() data: { room: string; userId?: string },
    @ConnectedSocket() client: Socket,
  ) {
    client.join(data.room);
    this.websocketService.joinRoom(client.id, data.room, data.userId);
    
    client.emit('joined-room', { room: data.room });
    client.to(data.room).emit('user-joined', { 
      userId: data.userId, 
      clientId: client.id 
    });
    
    this.logger.log(`客户端 ${client.id} 加入房间: ${data.room}`);
  }

  /**
   * 离开房间
   */
  @SubscribeMessage('leave-room')
  handleLeaveRoom(
    @MessageBody() data: { room: string },
    @ConnectedSocket() client: Socket,
  ) {
    client.leave(data.room);
    this.websocketService.leaveRoom(client.id, data.room);
    
    client.to(data.room).emit('user-left', { clientId: client.id });
    
    this.logger.log(`客户端 ${client.id} 离开房间: ${data.room}`);
  }

  /**
   * UI模板实时协作
   */
  @SubscribeMessage('template-edit')
  handleTemplateEdit(
    @MessageBody() data: { 
      templateId: string; 
      operation: string; 
      data: any; 
      userId: string 
    },
    @ConnectedSocket() client: Socket,
  ) {
    const room = `template:${data.templateId}`;
    
    // 广播给房间内其他用户
    client.to(room).emit('template-updated', {
      operation: data.operation,
      data: data.data,
      userId: data.userId,
      timestamp: new Date().toISOString(),
    });
    
    this.logger.log(`模板编辑事件: ${data.templateId} - ${data.operation}`);
  }

  /**
   * UI主题变更通知
   */
  @SubscribeMessage('theme-change')
  handleThemeChange(
    @MessageBody() data: { themeId: string; userId: string },
    @ConnectedSocket() client: Socket,
  ) {
    // 广播主题变更
    this.server.emit('theme-changed', {
      themeId: data.themeId,
      userId: data.userId,
      timestamp: new Date().toISOString(),
    });
    
    this.logger.log(`主题变更: ${data.themeId}`);
  }

  /**
   * UI配置同步
   */
  @SubscribeMessage('config-sync')
  handleConfigSync(
    @MessageBody() data: { configType: string; configData: any; userId: string },
    @ConnectedSocket() client: Socket,
  ) {
    const room = `config:${data.configType}`;
    
    // 广播配置变更
    client.to(room).emit('config-updated', {
      configType: data.configType,
      configData: data.configData,
      userId: data.userId,
      timestamp: new Date().toISOString(),
    });
    
    this.logger.log(`配置同步: ${data.configType}`);
  }

  /**
   * 实时预览
   */
  @SubscribeMessage('preview-update')
  handlePreviewUpdate(
    @MessageBody() data: { 
      previewId: string; 
      previewData: any; 
      userId: string 
    },
    @ConnectedSocket() client: Socket,
  ) {
    const room = `preview:${data.previewId}`;
    
    // 广播预览更新
    client.to(room).emit('preview-updated', {
      previewData: data.previewData,
      userId: data.userId,
      timestamp: new Date().toISOString(),
    });
    
    this.logger.log(`预览更新: ${data.previewId}`);
  }

  /**
   * 发送通知到指定房间
   */
  sendToRoom(room: string, event: string, data: any) {
    this.server.to(room).emit(event, data);
  }

  /**
   * 发送通知到所有客户端
   */
  broadcast(event: string, data: any) {
    this.server.emit(event, data);
  }
}
