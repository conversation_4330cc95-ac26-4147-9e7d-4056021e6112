# 可视化脚本服务 (Visual Script Service)

DL引擎可视化脚本编辑、执行、协作和管理服务

## 功能特性

### 🎨 核心功能
- **可视化脚本编辑**: 拖拽式节点编辑器，支持复杂逻辑构建
- **脚本执行引擎**: 高性能脚本执行，支持异步和同步执行
- **版本控制系统**: 完整的版本历史记录和回滚功能
- **实时协作**: 多用户同时编辑，实时同步更新
- **模板管理**: 脚本模板创建、分享和使用
- **执行监控**: 脚本执行状态监控和日志记录

### 🚀 技术特性
- **高性能**: 基于NestJS框架，支持高并发处理
- **队列系统**: Bull队列处理脚本执行任务
- **实时通信**: WebSocket支持实时协作
- **数据持久化**: MySQL数据库，可靠的数据存储
- **缓存优化**: Redis缓存，提升响应速度
- **微服务架构**: 支持分布式部署和扩展

## 快速开始

### 环境要求
- Node.js >= 18.0.0
- MySQL >= 8.0
- Redis >= 6.0

### 安装依赖
```bash
npm install
```

### 环境配置
```bash
# 复制环境变量配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

### 数据库初始化
```bash
# 创建MySQL数据库
mysql -u root -p
CREATE DATABASE dlengine;

# 运行数据库迁移
npm run migration:run
```

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run start:prod

# 调试模式
npm run start:debug
```

## API文档

启动服务后访问: http://localhost:3050/api/docs

## 主要模块

### 可视化脚本模块 (VisualScriptModule)
- 脚本CRUD操作
- 脚本内容管理
- 脚本权限控制
- 脚本分享和协作

### 执行模块 (ExecutionModule)
- 脚本执行管理
- 执行队列处理
- 执行状态监控
- 执行日志记录

### 版本控制模块 (VersionModule)
- 版本历史管理
- 版本比较和差异
- 版本恢复功能
- 版本标签管理

### 模板模块 (TemplateModule)
- 模板创建和管理
- 模板分类和标签
- 模板使用统计
- 模板分享功能

### 协作模块 (CollaborationModule)
- 实时协作编辑
- 协作者权限管理
- 实时通信
- 冲突解决

### 健康检查模块 (HealthModule)
- 服务健康监控
- 依赖服务检查
- 性能指标收集

## 环境变量配置

### 应用配置
```env
NODE_ENV=development
PORT=3050
HOST=0.0.0.0
```

### 数据库配置
```env
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=dlengine
```

### Redis配置
```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
```

### 脚本执行配置
```env
SCRIPT_TIMEOUT=30000
MAX_CONCURRENT_EXECUTIONS=10
EXECUTION_MEMORY_LIMIT=128
SANDBOX_ENABLED=true
```

## 开发指南

### 项目结构
```
src/
├── visual-script/        # 可视化脚本模块
│   ├── entities/         # 数据实体
│   ├── dto/              # 数据传输对象
│   ├── collaboration/    # 协作功能
│   └── *.controller.ts   # 控制器
├── execution/            # 执行模块
├── health/               # 健康检查模块
├── app.module.ts         # 根模块
└── main.ts              # 应用入口
```

### 运行命令
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run start:prod

# 构建
npm run build

# 测试
npm run test

# 代码检查
npm run lint

# 代码格式化
npm run format
```

## 部署说明

### Docker 部署
```bash
# 构建镜像
docker build -t visual-script-service .

# 运行容器
docker run -p 3050:3050 visual-script-service
```

### 环境变量
确保设置以下环境变量：
- `DB_HOST`: MySQL主机地址
- `DB_PASSWORD`: MySQL密码
- `REDIS_HOST`: Redis主机地址

## 监控和日志

### 健康检查端点
- `/health` - 完整健康检查
- `/health/ready` - 就绪检查
- `/health/live` - 存活检查

### 日志配置
日志文件位置: `./logs/`
日志级别: `debug`, `info`, `warn`, `error`

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

- 项目主页: [DL Engine](https://github.com/dl-engine)
- 问题反馈: [Issues](https://github.com/dl-engine/visual-script-service/issues)
- 邮箱: <EMAIL>
