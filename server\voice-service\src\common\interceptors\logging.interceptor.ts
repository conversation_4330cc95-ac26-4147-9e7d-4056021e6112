import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Lo<PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Request, Response } from 'express';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    const { method, url, ip } = request;
    const userAgent = request.get('User-Agent') || '';

    const now = Date.now();

    this.logger.log(
      `${method} ${url} - ${ip} - ${userAgent} - 开始处理`,
    );

    return next.handle().pipe(
      tap(() => {
        const { statusCode } = response;
        const contentLength = response.get('content-length') || 0;
        const duration = Date.now() - now;

        this.logger.log(
          `${method} ${url} - ${statusCode} - ${contentLength}bytes - ${duration}ms`,
        );
      }),
    );
  }
}

@Injectable()
export class TransformInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      tap((data) => {
        // 可以在这里对响应数据进行转换
        if (data && typeof data === 'object') {
          // 添加时间戳
          data.timestamp = new Date().toISOString();
          
          // 添加请求ID（如果存在）
          const request = context.switchToHttp().getRequest();
          if (request.headers['x-request-id']) {
            data.requestId = request.headers['x-request-id'];
          }
        }
      }),
    );
  }
}
