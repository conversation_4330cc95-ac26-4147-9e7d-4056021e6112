import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  UploadedFile,
  UseInterceptors,
  UseGuards,
  Request,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { VoiceService } from './voice.service';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import {
  SpeechRecognitionConfig,
  SpeechProvider,
} from '../speech-recognition/speech-recognition.service';
import {
  SpeechSynthesisConfig,
  TTSProvider,
} from '../speech-synthesis/speech-synthesis.service';
import {
  AudioProcessingConfig,
  AudioFormat,
} from '../audio-processing/audio-processing.service';
import {
  LipSyncConfig,
} from '../lip-sync/lip-sync.service';
import {
  SpeechRecognitionRequestDto,
  SpeechSynthesisRequestDto,
  AudioProcessingRequestDto,
  LipSyncRequestDto,
} from '../common/dto/voice.dto';

@ApiTags('voice')
@Controller('voice')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class VoiceController {
  constructor(private readonly voiceService: VoiceService) {}

  @Post('recognize')
  @ApiOperation({ summary: '语音识别' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        audio: {
          type: 'string',
          format: 'binary',
          description: '音频文件',
        },
        provider: {
          type: 'string',
          enum: Object.values(SpeechProvider),
          description: '语音识别提供商',
        },
        language: {
          type: 'string',
          description: '语言代码',
          example: 'zh-CN',
        },
        enableWordTimestamps: {
          type: 'boolean',
          description: '是否启用词时间戳',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '识别成功',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        text: { type: 'string' },
        confidence: { type: 'number' },
        language: { type: 'string' },
        duration: { type: 'number' },
        words: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              word: { type: 'string' },
              startTime: { type: 'number' },
              endTime: { type: 'number' },
              confidence: { type: 'number' },
            },
          },
        },
        provider: { type: 'string' },
        processingTime: { type: 'number' },
      },
    },
  })
  @UseInterceptors(FileInterceptor('audio'))
  async recognizeSpeech(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: any,
  ) {
    if (!file) {
      throw new BadRequestException('请上传音频文件');
    }

    const config: SpeechRecognitionConfig = {
      provider: body.provider || SpeechProvider.AZURE,
      language: body.language || 'zh-CN',
      enableWordTimestamps: body.enableWordTimestamps === 'true',
    };

    return this.voiceService.recognizeSpeech(file.buffer, config);
  }

  @Post('synthesize')
  @ApiOperation({ summary: '语音合成' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        text: {
          type: 'string',
          description: '要合成的文本',
          example: '您好，欢迎使用语音合成服务。',
        },
        provider: {
          type: 'string',
          enum: Object.values(TTSProvider),
          description: '语音合成提供商',
        },
        voice: {
          type: 'string',
          description: '语音名称',
          example: 'zh-CN-XiaoxiaoNeural',
        },
        language: {
          type: 'string',
          description: '语言代码',
          example: 'zh-CN',
        },
        rate: {
          type: 'number',
          description: '语速 (0.5-2.0)',
          minimum: 0.5,
          maximum: 2.0,
        },
        pitch: {
          type: 'number',
          description: '音调 (0.5-2.0)',
          minimum: 0.5,
          maximum: 2.0,
        },
        volume: {
          type: 'number',
          description: '音量 (0.0-1.0)',
          minimum: 0.0,
          maximum: 1.0,
        },
        style: {
          type: 'string',
          description: '语音风格',
          example: 'friendly',
        },
        emotion: {
          type: 'string',
          description: '情感',
          example: 'happy',
        },
        outputFormat: {
          type: 'string',
          enum: ['wav', 'mp3', 'ogg'],
          description: '输出格式',
        },
      },
      required: ['text'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '合成成功',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        audioData: { type: 'string', format: 'base64' },
        duration: { type: 'number' },
        format: { type: 'string' },
        sampleRate: { type: 'number' },
        voice: { type: 'string' },
        language: { type: 'string' },
        provider: { type: 'string' },
        processingTime: { type: 'number' },
      },
    },
  })
  async synthesizeSpeech(@Body() body: any) {
    if (!body.text) {
      throw new BadRequestException('请提供要合成的文本');
    }

    const config: SpeechSynthesisConfig = {
      provider: body.provider || TTSProvider.AZURE,
      voice: body.voice || 'zh-CN-XiaoxiaoNeural',
      language: body.language || 'zh-CN',
      rate: body.rate || 1.0,
      pitch: body.pitch || 1.0,
      volume: body.volume || 1.0,
      style: body.style,
      emotion: body.emotion,
      outputFormat: body.outputFormat || 'wav',
    };

    const result = await this.voiceService.synthesizeSpeech(body.text, config);
    
    // 将音频数据转换为base64
    return {
      ...result,
      audioData: result.audioData.toString('base64'),
    };
  }

  @Post('process-audio')
  @ApiOperation({ summary: '音频处理' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        audio: {
          type: 'string',
          format: 'binary',
          description: '音频文件',
        },
        outputFormat: {
          type: 'string',
          enum: Object.values(AudioFormat),
          description: '输出格式',
        },
        sampleRate: {
          type: 'number',
          description: '采样率',
        },
        channels: {
          type: 'number',
          description: '声道数',
        },
        volume: {
          type: 'number',
          description: '音量调整',
        },
        normalize: {
          type: 'boolean',
          description: '是否标准化',
        },
        removeNoise: {
          type: 'boolean',
          description: '是否降噪',
        },
        trimSilence: {
          type: 'boolean',
          description: '是否去除静音',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('audio'))
  async processAudio(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: any,
  ) {
    if (!file) {
      throw new BadRequestException('请上传音频文件');
    }

    const config: AudioProcessingConfig = {
      outputFormat: body.outputFormat || AudioFormat.WAV,
      sampleRate: body.sampleRate ? parseInt(body.sampleRate) : undefined,
      channels: body.channels ? parseInt(body.channels) : undefined,
      volume: body.volume ? parseFloat(body.volume) : undefined,
      normalize: body.normalize === 'true',
      removeNoise: body.removeNoise === 'true',
      trimSilence: body.trimSilence === 'true',
    };

    const processedAudio = await this.voiceService.processAudio(file.buffer, config);
    
    return {
      audioData: processedAudio.toString('base64'),
      format: config.outputFormat,
    };
  }

  @Post('generate-lip-sync')
  @ApiOperation({ summary: '生成嘴形同步数据' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        audio: {
          type: 'string',
          format: 'binary',
          description: '音频文件（可选）',
        },
        text: {
          type: 'string',
          description: '文本内容',
        },
        method: {
          type: 'string',
          enum: ['phoneme', 'audio', 'hybrid'],
          description: '生成方法',
        },
        language: {
          type: 'string',
          description: '语言代码',
          example: 'zh-CN',
        },
        frameRate: {
          type: 'number',
          description: '帧率',
          default: 30,
        },
        smoothing: {
          type: 'number',
          description: '平滑度 (0.0-1.0)',
          minimum: 0.0,
          maximum: 1.0,
        },
        intensity: {
          type: 'number',
          description: '强度 (0.0-1.0)',
          minimum: 0.0,
          maximum: 1.0,
        },
      },
      required: ['text'],
    },
  })
  @UseInterceptors(FileInterceptor('audio'))
  async generateLipSync(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: any,
  ) {
    if (!body.text) {
      throw new BadRequestException('请提供文本内容');
    }

    const config: LipSyncConfig = {
      method: body.method || 'phoneme',
      language: body.language || 'zh-CN',
      frameRate: body.frameRate ? parseInt(body.frameRate) : 30,
      smoothing: body.smoothing ? parseFloat(body.smoothing) : 0.5,
      intensity: body.intensity ? parseFloat(body.intensity) : 1.0,
    };

    const audioBuffer = file ? file.buffer : Buffer.alloc(0);
    
    return this.voiceService.generateLipSync(audioBuffer, body.text, config);
  }

  @Get('voices')
  @ApiOperation({ summary: '获取可用语音列表' })
  @ApiQuery({
    name: 'provider',
    enum: Object.values(TTSProvider),
    required: false,
    description: '语音合成提供商',
  })
  @ApiQuery({
    name: 'language',
    required: false,
    description: '语言代码',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          name: { type: 'string' },
          displayName: { type: 'string' },
          gender: { type: 'string' },
          language: { type: 'string' },
          styles: { type: 'array', items: { type: 'string' } },
        },
      },
    },
  })
  async getAvailableVoices(
    @Query('provider') provider?: TTSProvider,
    @Query('language') language?: string,
  ) {
    return this.voiceService.getAvailableVoices(
      provider || TTSProvider.AZURE,
      language,
    );
  }

  @Get('languages')
  @ApiOperation({ summary: '获取支持的语言列表' })
  @ApiQuery({
    name: 'provider',
    enum: Object.values(SpeechProvider),
    required: false,
    description: '语音服务提供商',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    schema: {
      type: 'array',
      items: { type: 'string' },
    },
  })
  async getSupportedLanguages(@Query('provider') provider?: SpeechProvider) {
    return this.voiceService.getSupportedLanguages(provider || SpeechProvider.AZURE);
  }

  @Post('preview-voice')
  @ApiOperation({ summary: '语音预览' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        voice: {
          type: 'string',
          description: '语音名称',
          example: 'zh-CN-XiaoxiaoNeural',
        },
        language: {
          type: 'string',
          description: '语言代码',
          example: 'zh-CN',
        },
        provider: {
          type: 'string',
          enum: Object.values(TTSProvider),
          description: '语音合成提供商',
        },
      },
      required: ['voice', 'language'],
    },
  })
  async previewVoice(@Body() body: any) {
    const result = await this.voiceService.previewVoice(
      body.voice,
      body.language,
      body.provider || TTSProvider.AZURE,
    );
    
    return {
      ...result,
      audioData: result.audioData.toString('base64'),
    };
  }

  @Get('statistics')
  @ApiOperation({ summary: '获取服务统计信息' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        speechRecognition: {
          type: 'object',
          properties: {
            activeRealtimeSessions: { type: 'number' },
            supportedProviders: { type: 'array', items: { type: 'string' } },
            totalProcessed: { type: 'number' },
          },
        },
        speechSynthesis: {
          type: 'object',
          properties: {
            supportedProviders: { type: 'array', items: { type: 'string' } },
            totalSynthesized: { type: 'number' },
            cacheSize: { type: 'number' },
          },
        },
        audioProcessing: {
          type: 'object',
          properties: {
            supportedFormats: {
              type: 'object',
              properties: {
                input: { type: 'array', items: { type: 'string' } },
                output: { type: 'array', items: { type: 'string' } },
              },
            },
          },
        },
        lipSync: {
          type: 'object',
          properties: {
            supportedLanguages: { type: 'array', items: { type: 'string' } },
            supportedVisemes: { type: 'array', items: { type: 'string' } },
          },
        },
      },
    },
  })
  async getStatistics() {
    return this.voiceService.getStatistics();
  }

  @Post('analyze-audio')
  @ApiOperation({ summary: '分析音频' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('audio'))
  async analyzeAudio(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('请上传音频文件');
    }

    return this.voiceService.analyzeAudio(file.buffer);
  }
}
