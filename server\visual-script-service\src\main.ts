/**
 * 可视化脚本服务启动文件
 * 提供可视化脚本编辑、执行、协作和管理功能
 */

import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { IoAdapter } from '@nestjs/platform-socket.io';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('VisualScriptService');

  try {
    // 创建应用实例
    const app = await NestFactory.create(AppModule, {
      logger: ['log', 'error', 'warn', 'debug', 'verbose'],
    });

    // 获取配置服务
    const configService = app.get(ConfigService);

    // 设置全局前缀
    app.setGlobalPrefix('api/v1');

    // 启用CORS
    app.enableCors({
      origin: configService.get<string>('CORS_ORIGIN', '*'),
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
      credentials: true,
    });

    // 全局验证管道
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
        disableErrorMessages: configService.get<string>('NODE_ENV') === 'production',
      }),
    );

    // WebSocket适配器
    app.useWebSocketAdapter(new IoAdapter(app));

    // Swagger API文档配置
    if (configService.get<string>('NODE_ENV') !== 'production') {
      const config = new DocumentBuilder()
        .setTitle('可视化脚本服务 API')
        .setDescription('DL引擎可视化脚本编辑、执行、协作和管理服务API文档')
        .setVersion('1.0.0')
        .addTag('visual-scripts', '可视化脚本管理')
        .addTag('script-execution', '脚本执行')
        .addTag('script-versions', '脚本版本管理')
        .addTag('script-templates', '脚本模板管理')
        .addTag('collaboration', '协作功能')
        .addTag('websocket', 'WebSocket实时通信')
        .addTag('health', '健康检查')
        .addBearerAuth()
        .addServer(`http://localhost:${configService.get<number>('PORT', 3050)}`, '本地开发服务器')
        .build();

      const document = SwaggerModule.createDocument(app, config);
      SwaggerModule.setup('api/docs', app, document, {
        swaggerOptions: {
          persistAuthorization: true,
          tagsSorter: 'alpha',
          operationsSorter: 'alpha',
        },
      });
    }

    // 微服务配置
    const microserviceOptions: MicroserviceOptions = {
      transport: Transport.TCP,
      options: {
        host: configService.get<string>('MICROSERVICE_HOST', '0.0.0.0'),
        port: configService.get<number>('MICROSERVICE_PORT', 3051),
        retryAttempts: 5,
        retryDelay: 3000,
      },
    };

    // 连接微服务
    app.connectMicroservice(microserviceOptions);
    await app.startAllMicroservices();

    // 启动HTTP服务
    const port = configService.get<number>('PORT', 3050);
    const host = configService.get<string>('HOST', '0.0.0.0');

    await app.listen(port, host);

    logger.log(`可视化脚本服务已启动`);
    logger.log(`HTTP服务: http://${host}:${port}`);
    logger.log(`API文档: http://${host}:${port}/api/docs`);
    logger.log(`微服务: tcp://${microserviceOptions.options.host}:${microserviceOptions.options.port}`);
    logger.log(`环境: ${configService.get<string>('NODE_ENV', 'development')}`);

    // 优雅关闭处理
    process.on('SIGTERM', async () => {
      logger.log('收到SIGTERM信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.log('收到SIGINT信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

  } catch (error) {
    logger.error('启动失败:', error);
    process.exit(1);
  }
}

bootstrap();
