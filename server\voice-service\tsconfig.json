{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["src/*"], "@/common/*": ["src/common/*"], "@/voice/*": ["src/voice/*"], "@/speech-recognition/*": ["src/speech-recognition/*"], "@/speech-synthesis/*": ["src/speech-synthesis/*"], "@/audio-processing/*": ["src/audio-processing/*"], "@/lip-sync/*": ["src/lip-sync/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*spec.ts"]}