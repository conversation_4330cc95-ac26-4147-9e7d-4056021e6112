#!/bin/bash

# DL引擎微服务项目完善实施脚本
# 用于自动化执行项目完善计划

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查项目状态
check_project_status() {
    local project_name=$1
    local project_path="server/$project_name"
    
    if [ ! -d "$project_path" ]; then
        log_error "项目目录不存在: $project_path"
        return 1
    fi
    
    if [ ! -f "$project_path/package.json" ]; then
        log_warning "缺少 package.json: $project_name"
        return 1
    fi
    
    if [ ! -f "$project_path/tsconfig.json" ]; then
        log_warning "缺少 tsconfig.json: $project_name"
        return 1
    fi
    
    log_success "项目状态检查通过: $project_name"
    return 0
}

# 创建基础项目结构
create_basic_structure() {
    local project_name=$1
    local project_path="server/$project_name"
    
    log_info "为 $project_name 创建基础结构..."
    
    # 创建基础目录
    mkdir -p "$project_path/src"
    mkdir -p "$project_path/src/controllers"
    mkdir -p "$project_path/src/services"
    mkdir -p "$project_path/src/modules"
    mkdir -p "$project_path/src/dto"
    mkdir -p "$project_path/src/entities"
    mkdir -p "$project_path/src/common"
    mkdir -p "$project_path/src/common/guards"
    mkdir -p "$project_path/src/common/filters"
    mkdir -p "$project_path/src/common/interceptors"
    mkdir -p "$project_path/test"
    mkdir -p "$project_path/docs"
    
    log_success "基础结构创建完成: $project_name"
}

# 安装依赖
install_dependencies() {
    local project_name=$1
    local project_path="server/$project_name"
    
    log_info "为 $project_name 安装依赖..."
    
    cd "$project_path"
    
    if [ -f "package.json" ]; then
        npm install --legacy-peer-deps
        log_success "依赖安装完成: $project_name"
    else
        log_error "package.json 不存在，跳过依赖安装: $project_name"
    fi
    
    cd - > /dev/null
}

# 运行项目构建测试
test_project_build() {
    local project_name=$1
    local project_path="server/$project_name"
    
    log_info "测试 $project_name 构建..."
    
    cd "$project_path"
    
    if [ -f "package.json" ]; then
        # 检查是否有构建脚本
        if npm run | grep -q "build"; then
            npm run build
            log_success "构建测试通过: $project_name"
        else
            log_warning "没有构建脚本: $project_name"
        fi
    else
        log_error "package.json 不存在: $project_name"
    fi
    
    cd - > /dev/null
}

# 第一阶段：高优先级项目
phase1_projects=(
    "ai-service"
    "ai-engine-service" 
    "cloud-edge-orchestration-service"
    "project-service"
    "asset-service"
    "collaboration-service"
    "performance-service"
    "user-service"
)

# 第二阶段：中优先级项目
phase2_projects=(
    "game-server"
    "ui-service"
    "behavior-decision-service"
    "edge-enhancement"
    "ecosystem-service"
    "spatial-service"
    "human-machine-collaboration-service"
    "monitoring-service"
    "analytics-service"
    "enterprise-integration-service"
)

# 第三阶段：低优先级项目
phase3_projects=(
    "blockchain-service"
    "5g-network-service"
)

# 执行阶段性完善
execute_phase() {
    local phase_num=$1
    local phase_name=$2
    shift 2
    local projects=("$@")
    
    log_info "开始执行第${phase_num}阶段: $phase_name"
    echo "========================================"
    
    for project in "${projects[@]}"; do
        log_info "处理项目: $project"
        
        # 检查项目状态
        if check_project_status "$project"; then
            # 安装依赖
            install_dependencies "$project"
            
            # 测试构建
            test_project_build "$project"
        else
            log_warning "项目 $project 需要手动检查和修复"
        fi
        
        echo "----------------------------------------"
    done
    
    log_success "第${phase_num}阶段完成: $phase_name"
    echo "========================================"
}

# 生成项目状态报告
generate_status_report() {
    local report_file="server/项目状态报告_$(date +%Y%m%d_%H%M%S).md"
    
    log_info "生成项目状态报告..."
    
    cat > "$report_file" << EOF
# DL引擎微服务项目状态报告

生成时间: $(date)

## 项目状态概览

### 第一阶段项目 (高优先级)
EOF

    for project in "${phase1_projects[@]}"; do
        if check_project_status "$project" > /dev/null 2>&1; then
            echo "- ✅ $project - 状态良好" >> "$report_file"
        else
            echo "- ❌ $project - 需要修复" >> "$report_file"
        fi
    done
    
    cat >> "$report_file" << EOF

### 第二阶段项目 (中优先级)
EOF

    for project in "${phase2_projects[@]}"; do
        if check_project_status "$project" > /dev/null 2>&1; then
            echo "- ✅ $project - 状态良好" >> "$report_file"
        else
            echo "- ❌ $project - 需要修复" >> "$report_file"
        fi
    done
    
    cat >> "$report_file" << EOF

### 第三阶段项目 (低优先级)
EOF

    for project in "${phase3_projects[@]}"; do
        if check_project_status "$project" > /dev/null 2>&1; then
            echo "- ✅ $project - 状态良好" >> "$report_file"
        else
            echo "- ❌ $project - 需要修复" >> "$report_file"
        fi
    done
    
    log_success "状态报告已生成: $report_file"
}

# 主函数
main() {
    log_info "DL引擎微服务项目完善实施脚本启动"
    log_info "========================================"
    
    # 检查是否在正确的目录
    if [ ! -d "server" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 显示菜单
    echo "请选择要执行的操作:"
    echo "1. 执行第一阶段完善 (高优先级项目)"
    echo "2. 执行第二阶段完善 (中优先级项目)"  
    echo "3. 执行第三阶段完善 (低优先级项目)"
    echo "4. 执行全部阶段完善"
    echo "5. 生成项目状态报告"
    echo "6. 检查所有项目状态"
    echo "0. 退出"
    
    read -p "请输入选择 (0-6): " choice
    
    case $choice in
        1)
            execute_phase 1 "高优先级项目" "${phase1_projects[@]}"
            ;;
        2)
            execute_phase 2 "中优先级项目" "${phase2_projects[@]}"
            ;;
        3)
            execute_phase 3 "低优先级项目" "${phase3_projects[@]}"
            ;;
        4)
            execute_phase 1 "高优先级项目" "${phase1_projects[@]}"
            execute_phase 2 "中优先级项目" "${phase2_projects[@]}"
            execute_phase 3 "低优先级项目" "${phase3_projects[@]}"
            ;;
        5)
            generate_status_report
            ;;
        6)
            log_info "检查所有项目状态..."
            all_projects=("${phase1_projects[@]}" "${phase2_projects[@]}" "${phase3_projects[@]}")
            for project in "${all_projects[@]}"; do
                check_project_status "$project"
            done
            ;;
        0)
            log_info "退出脚本"
            exit 0
            ;;
        *)
            log_error "无效选择，请重新运行脚本"
            exit 1
            ;;
    esac
    
    log_success "脚本执行完成"
}

# 运行主函数
main "$@"
