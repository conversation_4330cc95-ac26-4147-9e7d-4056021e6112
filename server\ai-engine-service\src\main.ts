import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

async function bootstrap() {
  const logger = new Logger('AI-Engine-Service');
  
  console.log('正在创建AI引擎服务...');
  const app = await NestFactory.create(AppModule);
  console.log('AI引擎服务创建成功');
  
  // 启用CORS
  app.enableCors({
    origin: true,
    credentials: true,
  });

  // 设置全局前缀
  app.setGlobalPrefix('api/v1');

  // 配置Swagger文档
  const config = new DocumentBuilder()
    .setTitle('智慧工厂AI引擎服务')
    .setDescription('深度学习、联邦学习、自适应学习、分布式训练、模型优化、模型部署API')
    .setVersion('1.0')
    .addTag('ai-engine', 'AI引擎管理')
    .addTag('deep-learning', '深度学习')
    .addTag('federated-learning', '联邦学习')
    .addTag('adaptive-learning', '自适应学习')
    .addTag('分布式训练', '分布式模型训练')
    .addTag('模型优化', '模型压缩、量化、剪枝等优化')
    .addTag('模型部署', '模型部署管道和A/B测试')
    .addTag('model-training', '模型训练')
    .addTag('inference', '模型推理')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  const port = process.env.PORT || 3016;
  console.log(`正在启动AI引擎服务，端口: ${port}`);
  await app.listen(port);
  
  logger.log(`AI引擎服务已启动，端口: ${port}`);
  logger.log(`API文档地址: http://localhost:${port}/api/docs`);
  console.log(`✅ AI引擎服务启动成功！`);
  console.log(`🌐 API地址: http://localhost:${port}/api/v1`);
  console.log(`📚 API文档: http://localhost:${port}/api/docs`);
}

bootstrap().catch(err => {
  console.error('AI引擎服务启动失败:', err);
  process.exit(1);
});
