/**
 * Jest测试设置文件
 */

import { MongoMemoryServer } from 'mongodb-memory-server';
import { Test } from '@nestjs/testing';
import { MongooseModule } from '@nestjs/mongoose';

// 全局变量
declare global {
  var __MONGO_URI__: string;
  var __MONGO_DB_NAME__: string;
}

let mongod: MongoMemoryServer;

/**
 * 全局设置 - 在所有测试开始前执行
 */
beforeAll(async () => {
  // 启动内存MongoDB实例
  mongod = await MongoMemoryServer.create();
  const uri = mongod.getUri();
  const dbName = mongod.instanceInfo?.dbName || 'test';
  
  // 设置全局变量
  global.__MONGO_URI__ = uri;
  global.__MONGO_DB_NAME__ = dbName;
  
  // 设置环境变量
  process.env.MONGODB_URI = uri;
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test-secret';
  process.env.REDIS_HOST = 'localhost';
  process.env.REDIS_PORT = '6379';
});

/**
 * 全局清理 - 在所有测试结束后执行
 */
afterAll(async () => {
  if (mongod) {
    await mongod.stop();
  }
});

/**
 * 每个测试前的设置
 */
beforeEach(() => {
  // 清除所有模拟
  jest.clearAllMocks();
});

/**
 * 创建测试模块的辅助函数
 */
export const createTestingModule = async (imports: any[] = [], providers: any[] = []) => {
  return Test.createTestingModule({
    imports: [
      MongooseModule.forRoot(global.__MONGO_URI__),
      ...imports,
    ],
    providers,
  }).compile();
};

/**
 * 模拟Redis客户端
 */
export const mockRedisClient = {
  get: jest.fn(),
  set: jest.fn(),
  setex: jest.fn(),
  del: jest.fn(),
  keys: jest.fn(),
  ping: jest.fn().mockResolvedValue('PONG'),
  quit: jest.fn(),
  disconnect: jest.fn(),
};

/**
 * 模拟用户数据
 */
export const mockUser = {
  id: '507f1f77bcf86cd799439011',
  username: 'testuser',
  email: '<EMAIL>',
  roles: ['user'],
  organizationId: '507f1f77bcf86cd799439012',
};

/**
 * 模拟JWT载荷
 */
export const mockJwtPayload = {
  sub: mockUser.id,
  username: mockUser.username,
  email: mockUser.email,
  roles: mockUser.roles,
  organizationId: mockUser.organizationId,
};

/**
 * 模拟请求对象
 */
export const mockRequest = {
  user: mockJwtPayload,
  headers: {
    authorization: 'Bearer mock-token',
  },
  ip: '127.0.0.1',
  get: jest.fn(),
};

/**
 * 模拟响应对象
 */
export const mockResponse = {
  status: jest.fn().mockReturnThis(),
  json: jest.fn().mockReturnThis(),
  send: jest.fn().mockReturnThis(),
  cookie: jest.fn().mockReturnThis(),
  clearCookie: jest.fn().mockReturnThis(),
};

/**
 * 等待异步操作完成的辅助函数
 */
export const waitForAsync = (ms = 0) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * 生成随机字符串
 */
export const randomString = (length = 10) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

/**
 * 生成随机数字
 */
export const randomNumber = (min = 0, max = 100) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

/**
 * 生成模拟ObjectId
 */
export const mockObjectId = () => {
  return '507f1f77bcf86cd799439' + Math.random().toString(36).substr(2, 3);
};
