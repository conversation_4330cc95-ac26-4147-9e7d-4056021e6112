# UI服务 (UI Service)

DL引擎UI界面管理服务 - 提供UI模板、主题、组件管理等功能

## 功能特性

### 🎨 核心功能
- **UI模板管理**: 模板创建、编辑、版本控制、发布管理
- **UI主题管理**: 主题定制、颜色配置、字体设置、样式管理
- **UI组件管理**: 组件库管理、组件发布、依赖管理
- **UI配置管理**: 全局配置、用户配置、项目配置
- **版本控制**: 完整的版本历史记录和回滚功能
- **分析统计**: 使用情况分析、热门资源统计

### 🚀 技术特性
- **高性能**: 基于NestJS框架，支持高并发处理
- **实时协作**: WebSocket支持，多用户实时编辑
- **缓存优化**: Redis缓存，提升响应速度
- **数据持久化**: MongoDB数据库，灵活的文档存储
- **API文档**: Swagger自动生成API文档
- **微服务架构**: 支持分布式部署和扩展

## 快速开始

### 环境要求
- Node.js >= 16.0.0
- MongoDB >= 4.4
- Redis >= 6.0

### 安装依赖
```bash
npm install
```

### 环境配置
```bash
# 复制环境变量配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

### 数据库初始化
```bash
# 创建MongoDB数据库
mongo
use dl-ui-service
```

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run start:prod

# 调试模式
npm run start:debug
```

## API文档

启动服务后访问: http://localhost:3020/api/docs

## 主要模块

### UI模板模块 (UITemplateModule)
- 模板CRUD操作
- 模板版本管理
- 模板发布和分享
- 模板复制和派生

### UI主题模块 (UIThemeModule)
- 主题创建和编辑
- 颜色配置管理
- 字体和样式设置
- 主题预览和应用

### UI组件模块 (UIComponentModule)
- 组件库管理
- 组件属性定义
- 组件事件处理
- 组件样式配置

### UI配置模块 (UIConfigModule)
- 全局配置管理
- 用户个性化配置
- 项目级配置
- 配置权限控制

### 版本控制模块 (UIVersionModule)
- 版本历史记录
- 版本比较和回滚
- 变更日志管理

### 分析统计模块 (UIAnalyticsModule)
- 使用情况统计
- 热门资源分析
- 用户行为追踪

### WebSocket模块 (WebSocketModule)
- 实时协作支持
- 多用户同步编辑
- 实时通知推送

### 认证授权模块 (AuthModule)
- JWT令牌验证
- 权限控制
- 用户身份认证

### 健康检查模块 (HealthModule)
- 服务健康监控
- 依赖服务检查
- 性能指标收集

## 环境变量配置

### 应用配置
```env
NODE_ENV=development
PORT=3020
HOST=0.0.0.0
```

### 数据库配置
```env
MONGODB_URI=mongodb://localhost:27017/dl-ui-service
REDIS_HOST=localhost
REDIS_PORT=6379
```

### JWT配置
```env
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=24h
```

### 文件存储配置
```env
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760
```

## 开发指南

### 项目结构
```
src/
├── modules/              # 功能模块
│   ├── ui-template/      # UI模板模块
│   ├── ui-theme/         # UI主题模块
│   ├── ui-component/     # UI组件模块
│   ├── ui-config/        # UI配置模块
│   ├── ui-version/       # 版本控制模块
│   ├── ui-analytics/     # 分析统计模块
│   ├── websocket/        # WebSocket模块
│   ├── auth/             # 认证授权模块
│   └── health/           # 健康检查模块
├── common/               # 公共组件
├── config/               # 配置文件
└── main.ts              # 应用入口
```

### 运行命令
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run start:prod

# 构建
npm run build

# 测试
npm run test

# 代码检查
npm run lint

# 代码格式化
npm run format
```

## 部署说明

### Docker 部署
```bash
# 构建镜像
docker build -t ui-service .

# 运行容器
docker run -p 3020:3020 ui-service
```

### 环境变量
确保设置以下环境变量：
- `MONGODB_URI`: MongoDB连接字符串
- `REDIS_HOST`: Redis主机地址
- `JWT_SECRET`: JWT密钥

## 监控和日志

### 健康检查端点
- `/health` - 完整健康检查
- `/health/ready` - 就绪检查
- `/health/live` - 存活检查

### 日志配置
日志文件位置: `./logs/`
日志级别: `debug`, `info`, `warn`, `error`

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

- 项目主页: [DL Engine](https://github.com/dl-engine)
- 问题反馈: [Issues](https://github.com/dl-engine/ui-service/issues)
- 邮箱: <EMAIL>
