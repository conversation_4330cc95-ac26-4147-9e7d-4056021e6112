import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  Patch,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { ExecutionService } from './execution.service';
import { ExecuteScriptDto, QueryExecutionDto, UpdateExecutionDto } from './dto/execution.dto';

@ApiTags('script-execution')
@Controller('execution')
// @UseGuards(JwtAuthGuard) // 需要实现认证守卫
export class ExecutionController {
  constructor(private readonly executionService: ExecutionService) {}

  @Post('execute/:scriptId')
  @ApiOperation({ summary: '执行脚本' })
  @ApiParam({ name: 'scriptId', description: '脚本ID' })
  @ApiResponse({ status: HttpStatus.CREATED, description: '执行任务已创建' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: '请求参数错误' })
  @ApiBearerAuth()
  async executeScript(
    @Param('scriptId') scriptId: string,
    @Body() executeDto: ExecuteScriptDto,
    @Request() req: any,
  ) {
    const userId = req.user?.id || 'anonymous';
    return this.executionService.executeScript(scriptId, executeDto, userId);
  }

  @Get()
  @ApiOperation({ summary: '获取执行历史列表' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  @ApiBearerAuth()
  async getExecutions(@Query() queryDto: QueryExecutionDto, @Request() req: any) {
    const userId = req.user?.id || 'anonymous';
    return this.executionService.getExecutions(queryDto, userId);
  }

  @Get(':executionId')
  @ApiOperation({ summary: '获取执行详情' })
  @ApiParam({ name: 'executionId', description: '执行ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '执行记录不存在' })
  @ApiBearerAuth()
  async getExecution(@Param('executionId') executionId: string, @Request() req: any) {
    const userId = req.user?.id || 'anonymous';
    return this.executionService.getExecution(executionId, userId);
  }

  @Patch(':executionId/stop')
  @ApiOperation({ summary: '停止脚本执行' })
  @ApiParam({ name: 'executionId', description: '执行ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '停止成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '执行记录不存在' })
  @ApiBearerAuth()
  async stopExecution(@Param('executionId') executionId: string, @Request() req: any) {
    const userId = req.user?.id || 'anonymous';
    return this.executionService.stopExecution(executionId, userId);
  }

  @Delete(':executionId')
  @ApiOperation({ summary: '删除执行记录' })
  @ApiParam({ name: 'executionId', description: '执行ID' })
  @ApiResponse({ status: HttpStatus.NO_CONTENT, description: '删除成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '执行记录不存在' })
  @ApiBearerAuth()
  async deleteExecution(@Param('executionId') executionId: string, @Request() req: any) {
    const userId = req.user?.id || 'anonymous';
    await this.executionService.deleteExecution(executionId, userId);
  }

  @Get('script/:scriptId/executions')
  @ApiOperation({ summary: '获取脚本的执行历史' })
  @ApiParam({ name: 'scriptId', description: '脚本ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  @ApiBearerAuth()
  async getScriptExecutions(
    @Param('scriptId') scriptId: string,
    @Query() queryDto: QueryExecutionDto,
    @Request() req: any,
  ) {
    const userId = req.user?.id || 'anonymous';
    return this.executionService.getScriptExecutions(scriptId, queryDto, userId);
  }

  @Get(':executionId/logs')
  @ApiOperation({ summary: '获取执行日志' })
  @ApiParam({ name: 'executionId', description: '执行ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  @ApiBearerAuth()
  async getExecutionLogs(@Param('executionId') executionId: string, @Request() req: any) {
    const userId = req.user?.id || 'anonymous';
    return this.executionService.getExecutionLogs(executionId, userId);
  }

  @Get('stats/summary')
  @ApiOperation({ summary: '获取执行统计摘要' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  @ApiBearerAuth()
  async getExecutionStats(@Request() req: any) {
    const userId = req.user?.id || 'anonymous';
    return this.executionService.getExecutionStats(userId);
  }
}
