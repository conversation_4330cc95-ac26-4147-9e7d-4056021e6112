import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UIVersionController } from './ui-version.controller';
import { UIVersionService } from './ui-version.service';
import { UIVersion, UIVersionSchema } from './schemas/ui-version.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UIVersion.name, schema: UIVersionSchema },
    ]),
  ],
  controllers: [UIVersionController],
  providers: [UIVersionService],
  exports: [UIVersionService],
})
export class UIVersionModule {}
