import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { ScheduleModule } from '@nestjs/schedule';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { TerminusModule } from '@nestjs/terminus';

// 业务模块
import { VisualScriptModule } from './visual-script/visual-script.module';
import { ExecutionModule } from './execution/execution.module';
import { HealthModule } from './health/health.module';

// 实体
import { VisualScript } from './visual-script/entities/visual-script.entity';
import { ScriptVersion } from './visual-script/entities/script-version.entity';
import { ScriptExecution } from './visual-script/entities/script-execution.entity';
import { ScriptCollaborator } from './visual-script/entities/script-collaborator.entity';
import { ScriptTemplate } from './visual-script/entities/script-template.entity';
import { ScriptComment } from './visual-script/entities/script-comment.entity';
import { ScriptTag } from './visual-script/entities/script-tag.entity';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get<string>('DB_HOST', 'localhost'),
        port: configService.get<number>('DB_PORT', 3306),
        username: configService.get<string>('DB_USERNAME', 'root'),
        password: configService.get<string>('DB_PASSWORD', ''),
        database: configService.get<string>('DB_DATABASE', 'dlengine'),
        entities: [
          VisualScript,
          ScriptVersion,
          ScriptExecution,
          ScriptCollaborator,
          ScriptTemplate,
          ScriptComment,
          ScriptTag,
        ],
        synchronize: configService.get<string>('NODE_ENV') !== 'production',
        logging: configService.get<string>('NODE_ENV') === 'development',
        timezone: '+08:00',
        charset: 'utf8mb4',
        extra: {
          connectionLimit: 10,
        },
      }),
      inject: [ConfigService],
    }),

    // Redis队列模块
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get<string>('REDIS_HOST', 'localhost'),
          port: configService.get<number>('REDIS_PORT', 6379),
          password: configService.get<string>('REDIS_PASSWORD'),
          db: configService.get<number>('REDIS_DB', 0),
          maxRetriesPerRequest: 3,
          retryDelayOnFailover: 100,
          enableReadyCheck: false,
          maxLoadingTimeout: 0,
        },
      }),
      inject: [ConfigService],
    }),

    // 调度模块
    ScheduleModule.forRoot(),

    // 事件发射器模块
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),

    // 健康检查模块
    TerminusModule,

    // 业务模块
    VisualScriptModule,
    ExecutionModule,
    HealthModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
