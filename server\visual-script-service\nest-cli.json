{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "webpack": false, "tsConfigPath": "tsconfig.json", "assets": ["**/*.json", "**/*.md", "**/*.yml", "**/*.yaml"], "watchAssets": true}, "projects": {"visual-script-service": {"type": "application", "root": "", "entryFile": "main", "sourceRoot": "src", "compilerOptions": {"tsConfigPath": "tsconfig.json"}}}, "generateOptions": {"spec": true, "flat": false}}