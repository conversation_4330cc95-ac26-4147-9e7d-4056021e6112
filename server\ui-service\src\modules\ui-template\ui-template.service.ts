import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { UITemplate, UITemplateDocument, TemplateCategory, TemplateStatus, AccessLevel } from './schemas/ui-template.schema';
import { UITemplateVersion, UITemplateVersionDocument, VersionType } from './schemas/ui-template-version.schema';
import { CreateTemplateDto, UpdateTemplateDto, QueryTemplateDto } from './dto/ui-template.dto';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';

@Injectable()
export class UITemplateService {
  constructor(
    @InjectModel(UITemplate.name) private templateModel: Model<UITemplateDocument>,
    @InjectModel(UITemplateVersion.name) private versionModel: Model<UITemplateVersionDocument>,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  /**
   * 创建UI模板
   */
  async create(createTemplateDto: CreateTemplateDto, userId: Types.ObjectId): Promise<UITemplateDocument> {
    const template = new this.templateModel({
      ...createTemplateDto,
      createdBy: userId,
      updatedBy: userId,
    });

    const savedTemplate = await template.save();

    // 创建初始版本
    await this.createVersion(savedTemplate._id, {
      version: '1.0.0',
      versionType: VersionType.MAJOR,
      description: '初始版本',
      elements: createTemplateDto.elements || [],
      metadata: createTemplateDto.metadata,
    }, userId);

    // 清除相关缓存
    await this.clearTemplateCache(savedTemplate._id);

    return savedTemplate;
  }

  /**
   * 查找所有模板
   */
  async findAll(query: QueryTemplateDto, userId: Types.ObjectId, userOrganizationId?: Types.ObjectId, userTeamId?: Types.ObjectId): Promise<{
    templates: UITemplateDocument[];
    total: number;
    page: number;
    limit: number;
  }> {
    const {
      page = 1,
      limit = 20,
      category,
      status,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      accessLevel,
      createdBy,
      organizationId,
      projectId
    } = query;

    // 构建查询条件
    const filter: any = {};

    // 访问权限过滤
    const accessFilter = this.buildAccessFilter(userId, userOrganizationId, userTeamId);
    Object.assign(filter, accessFilter);

    if (category) filter.category = category;
    if (status) filter.status = status;
    if (accessLevel) filter.accessLevel = accessLevel;
    if (createdBy) filter.createdBy = new Types.ObjectId(createdBy);
    if (organizationId) filter.organizationId = new Types.ObjectId(organizationId);
    if (projectId) filter.projectId = new Types.ObjectId(projectId);

    // 搜索条件
    if (search) {
      filter.$text = { $search: search };
    }

    // 排序
    const sort: any = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // 分页
    const skip = (page - 1) * limit;

    const [templates, total] = await Promise.all([
      this.templateModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('createdBy', 'name email avatar')
        .populate('updatedBy', 'name email avatar')
        .exec(),
      this.templateModel.countDocuments(filter),
    ]);

    return {
      templates,
      total,
      page,
      limit,
    };
  }

  /**
   * 根据ID查找模板
   */
  async findOne(id: string, userId: Types.ObjectId, userOrganizationId?: Types.ObjectId, userTeamId?: Types.ObjectId): Promise<UITemplateDocument> {
    // 尝试从缓存获取
    const cacheKey = `template:${id}`;
    const cached = await this.redis.get(cacheKey);
    if (cached) {
      const template = JSON.parse(cached);
      // 检查访问权限
      if (!this.checkAccess(template, userId, userOrganizationId, userTeamId)) {
        throw new ForbiddenException('没有访问权限');
      }
      return template;
    }

    const template = await this.templateModel
      .findById(id)
      .populate('createdBy', 'name email avatar')
      .populate('updatedBy', 'name email avatar')
      .exec();

    if (!template) {
      throw new NotFoundException('模板不存在');
    }

    // 检查访问权限
    if (!this.canAccessTemplate(template, userId, userOrganizationId, userTeamId)) {
      throw new ForbiddenException('没有访问权限');
    }

    // 增加访问次数
    await this.incrementViews(template);

    // 缓存模板
    await this.redis.setex(cacheKey, 3600, JSON.stringify(template));

    return template;
  }

  /**
   * 更新模板
   */
  async update(id: string, updateTemplateDto: UpdateTemplateDto, userId: Types.ObjectId, userRole?: string): Promise<UITemplateDocument> {
    const template = await this.templateModel.findById(id);

    if (!template) {
      throw new NotFoundException('模板不存在');
    }

    // 检查编辑权限
    if (!this.canEditTemplate(template, userId, userRole)) {
      throw new ForbiddenException('没有编辑权限');
    }

    // 如果更新了元素，创建新版本
    if (updateTemplateDto.elements) {
      const latestVersion = await this.versionModel
        .findOne({ templateId: template._id })
        .sort({ createdAt: -1 });

      if (latestVersion) {
        const newVersionNumber = this.incrementVersion(latestVersion.version, VersionType.MINOR);
        await this.createVersion(template._id, {
          version: newVersionNumber,
          versionType: VersionType.MINOR,
          description: updateTemplateDto.versionDescription || '更新模板',
          elements: updateTemplateDto.elements,
          metadata: updateTemplateDto.metadata,
        }, userId);
      }
    }

    Object.assign(template, updateTemplateDto, { updatedBy: userId });
    const updatedTemplate = await template.save();

    // 清除缓存
    await this.clearTemplateCache(id);

    return updatedTemplate;
  }

  /**
   * 删除模板
   */
  async remove(id: string, userId: Types.ObjectId, userRole?: string): Promise<void> {
    const template = await this.templateModel.findById(id);

    if (!template) {
      throw new NotFoundException('模板不存在');
    }

    // 检查删除权限
    if (!this.canEditTemplate(template, userId, userRole)) {
      throw new ForbiddenException('没有删除权限');
    }

    // 软删除
    await this.softDeleteTemplate(template, userId);

    // 清除缓存
    await this.clearTemplateCache(id);
  }

  /**
   * 恢复模板
   */
  async restore(id: string, userId: Types.ObjectId, userRole?: string): Promise<UITemplateDocument> {
    const template = await this.templateModel.findOne({ _id: id, isDeleted: true });

    if (!template) {
      throw new NotFoundException('模板不存在或未被删除');
    }

    // 检查恢复权限
    if (!this.canEditTemplate(template, userId, userRole)) {
      throw new ForbiddenException('没有恢复权限');
    }

    await this.restoreTemplate(template);

    // 清除缓存
    await this.clearTemplateCache(id);

    return template;
  }

  /**
   * 复制模板
   */
  async fork(id: string, forkData: { name: string; description?: string }, userId: Types.ObjectId): Promise<UITemplateDocument> {
    const originalTemplate = await this.templateModel.findById(id);

    if (!originalTemplate) {
      throw new NotFoundException('原模板不存在');
    }

    // 检查是否允许复制
    if (!originalTemplate.settings.allowFork) {
      throw new ForbiddenException('该模板不允许复制');
    }

    // 获取最新版本的元素
    const latestVersion = await this.versionModel
      .findOne({ templateId: originalTemplate._id })
      .sort({ createdAt: -1 });

    const forkedTemplate = new this.templateModel({
      name: forkData.name,
      description: forkData.description || originalTemplate.description,
      category: originalTemplate.category,
      tags: [...originalTemplate.tags],
      elements: latestVersion?.elements || [],
      metadata: originalTemplate.metadata,
      parentTemplateId: originalTemplate._id,
      createdBy: userId,
      updatedBy: userId,
    });

    const savedTemplate = await forkedTemplate.save();

    // 创建初始版本
    await this.createVersion(savedTemplate._id, {
      version: '1.0.0',
      versionType: VersionType.MAJOR,
      description: `从模板 ${originalTemplate.name} 复制`,
      elements: latestVersion?.elements || [],
      metadata: originalTemplate.metadata,
    }, userId);

    // 更新原模板的统计信息
    originalTemplate.statistics.forks += 1;
    await originalTemplate.save();

    return savedTemplate;
  }

  /**
   * 发布模板
   */
  async publish(id: string, userId: Types.ObjectId, userRole?: string): Promise<UITemplateDocument> {
    const template = await this.templateModel.findById(id);

    if (!template) {
      throw new NotFoundException('模板不存在');
    }

    // 检查发布权限
    if (!this.canEditTemplate(template, userId, userRole)) {
      throw new ForbiddenException('没有发布权限');
    }

    if (template.status === TemplateStatus.PUBLISHED) {
      throw new BadRequestException('模板已经发布');
    }

    template.status = TemplateStatus.PUBLISHED;
    template.publishedAt = new Date();
    template.updatedBy = userId;

    const publishedTemplate = await template.save();

    // 清除缓存
    await this.clearTemplateCache(id);

    return publishedTemplate;
  }

  /**
   * 创建版本
   */
  async createVersion(templateId: Types.ObjectId, versionData: any, userId: Types.ObjectId): Promise<UITemplateVersionDocument> {
    const version = new this.versionModel({
      templateId,
      ...versionData,
      createdBy: userId,
    });

    return await version.save();
  }

  /**
   * 获取版本历史
   */
  async getVersionHistory(templateId: string, limit: number = 50): Promise<UITemplateVersionDocument[]> {
    return await this.versionModel
      .find({ templateId: new Types.ObjectId(templateId) })
      .sort({ createdAt: -1 })
      .limit(limit)
      .populate('createdBy', 'name email avatar')
      .exec();
  }

  /**
   * 构建访问权限过滤器
   */
  private buildAccessFilter(userId: Types.ObjectId, userOrganizationId?: Types.ObjectId, userTeamId?: Types.ObjectId): any {
    const accessFilter = {
      $or: [
        { accessLevel: AccessLevel.PUBLIC },
        { createdBy: userId },
      ]
    };

    if (userOrganizationId) {
      accessFilter.$or.push({
        accessLevel: AccessLevel.ORGANIZATION,
        organizationId: userOrganizationId
      } as any);
    }

    if (userTeamId) {
      accessFilter.$or.push({
        accessLevel: AccessLevel.TEAM,
        teamId: userTeamId
      } as any);
    }

    return accessFilter;
  }

  /**
   * 检查访问权限
   */
  private checkAccess(template: any, userId: Types.ObjectId, userOrganizationId?: Types.ObjectId, userTeamId?: Types.ObjectId): boolean {
    if (template.accessLevel === AccessLevel.PUBLIC) return true;
    if (template.createdBy.toString() === userId.toString()) return true;
    if (template.accessLevel === AccessLevel.ORGANIZATION && userOrganizationId && template.organizationId === userOrganizationId.toString()) return true;
    if (template.accessLevel === AccessLevel.TEAM && userTeamId && template.teamId === userTeamId.toString()) return true;
    return false;
  }

  /**
   * 版本号递增
   */
  private incrementVersion(currentVersion: string, versionType: VersionType): string {
    const parts = currentVersion.split('.').map(Number);
    
    switch (versionType) {
      case VersionType.MAJOR:
        parts[0] += 1;
        parts[1] = 0;
        parts[2] = 0;
        break;
      case VersionType.MINOR:
        parts[1] += 1;
        parts[2] = 0;
        break;
      case VersionType.PATCH:
        parts[2] += 1;
        break;
    }
    
    return parts.join('.');
  }

  /**
   * 清除模板缓存
   */
  private async clearTemplateCache(templateId: Types.ObjectId | string): Promise<void> {
    const cacheKey = `template:${templateId}`;
    await this.redis.del(cacheKey);
  }

  /**
   * 检查模板访问权限
   */
  private canAccessTemplate(template: any, userId: Types.ObjectId, userOrganizationId?: Types.ObjectId, userTeamId?: Types.ObjectId): boolean {
    if (template.accessLevel === 'public') return true;
    if (template.createdBy.toString() === userId.toString()) return true;
    if (template.accessLevel === 'organization' && userOrganizationId && template.organizationId?.toString() === userOrganizationId.toString()) return true;
    if (template.accessLevel === 'team' && userTeamId && template.teamId?.toString() === userTeamId.toString()) return true;
    return false;
  }

  /**
   * 检查模板编辑权限
   */
  private canEditTemplate(template: any, userId: Types.ObjectId, userRole?: string): boolean {
    if (template.createdBy.toString() === userId.toString()) return true;
    if (userRole === 'admin') return true;
    return false;
  }

  /**
   * 增加浏览次数
   */
  private async incrementViews(template: any): Promise<void> {
    template.statistics = template.statistics || {};
    template.statistics.views = (template.statistics.views || 0) + 1;
    await template.save();
  }

  /**
   * 软删除模板
   */
  private async softDeleteTemplate(template: any, userId: Types.ObjectId): Promise<void> {
    template.deletedAt = new Date();
    template.deletedBy = userId;
    template.isActive = false;
    await template.save();
  }

  /**
   * 恢复模板
   */
  private async restoreTemplate(template: any): Promise<void> {
    template.deletedAt = undefined;
    template.deletedBy = undefined;
    template.isActive = true;
    await template.save();
  }
}
