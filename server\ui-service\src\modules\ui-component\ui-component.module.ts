import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UIComponentController } from './ui-component.controller';
import { UIComponentService } from './ui-component.service';
import { UIComponent, UIComponentSchema } from './schemas/ui-component.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UIComponent.name, schema: UIComponentSchema },
    ]),
  ],
  controllers: [UIComponentController],
  providers: [UIComponentService],
  exports: [UIComponentService],
})
export class UIComponentModule {}
