/**
 * 分布式训练控制器
 * 提供分布式训练相关的API接口
 */

import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { 
  DistributedTrainingService, 
  TrainingNode, 
  DistributedTrainingJob 
} from './distributed-training.service';

@ApiTags('分布式训练')
@Controller('api/v1/distributed-training')
export class DistributedTrainingController {
  private readonly logger = new Logger(DistributedTrainingController.name);

  constructor(private readonly distributedTrainingService: DistributedTrainingService) {}

  /**
   * 注册训练节点
   */
  @Post('nodes')
  @ApiOperation({ summary: '注册新的训练节点' })
  @ApiResponse({ status: 201, description: '节点注册成功' })
  @ApiResponse({ status: 400, description: '注册参数错误' })
  async registerTrainingNode(@Body() nodeConfig: Partial<TrainingNode>): Promise<{ nodeId: string }> {
    try {
      this.logger.log(`注册训练节点: ${nodeConfig.endpoint}`);
      
      const nodeId = await this.distributedTrainingService.registerTrainingNode(nodeConfig);
      
      this.logger.log(`训练节点注册成功: ${nodeId}`);
      return { nodeId };

    } catch (error) {
      this.logger.error(`注册训练节点失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 获取训练节点列表
   */
  @Get('nodes')
  @ApiOperation({ summary: '获取所有训练节点' })
  @ApiQuery({ name: 'status', required: false, description: '节点状态过滤' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getTrainingNodes(@Query('status') status?: string): Promise<TrainingNode[]> {
    try {
      let nodes = this.distributedTrainingService.getTrainingNodes();
      
      if (status) {
        nodes = nodes.filter(node => node.status === status);
      }

      this.logger.log(`获取训练节点列表: ${nodes.length} 个节点`);
      return nodes;

    } catch (error) {
      this.logger.error(`获取训练节点列表失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 创建分布式训练任务
   */
  @Post('jobs')
  @ApiOperation({ summary: '创建分布式训练任务' })
  @ApiResponse({ status: 201, description: '任务创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async createTrainingJob(@Body() jobConfig: any): Promise<{ jobId: string }> {
    try {
      this.logger.log(`创建分布式训练任务: ${jobConfig.modelId}`);
      
      const jobId = await this.distributedTrainingService.createDistributedTrainingJob(jobConfig);
      
      this.logger.log(`分布式训练任务创建成功: ${jobId}`);
      return { jobId };

    } catch (error) {
      this.logger.error(`创建分布式训练任务失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 开始分布式训练
   */
  @Post('jobs/:jobId/start')
  @ApiOperation({ summary: '开始分布式训练任务' })
  @ApiParam({ name: 'jobId', description: '训练任务ID' })
  @ApiResponse({ status: 200, description: '训练开始成功' })
  @ApiResponse({ status: 404, description: '任务不存在' })
  async startTraining(@Param('jobId') jobId: string): Promise<{ message: string }> {
    try {
      this.logger.log(`开始分布式训练: ${jobId}`);
      
      await this.distributedTrainingService.startDistributedTraining(jobId);
      
      this.logger.log(`分布式训练开始成功: ${jobId}`);
      return { message: '分布式训练已开始' };

    } catch (error) {
      this.logger.error(`开始分布式训练失败: ${error.message}`);
      if (error.message.includes('不存在')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 获取训练任务状态
   */
  @Get('jobs/:jobId')
  @ApiOperation({ summary: '获取训练任务状态' })
  @ApiParam({ name: 'jobId', description: '训练任务ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '任务不存在' })
  async getTrainingJobStatus(@Param('jobId') jobId: string): Promise<DistributedTrainingJob> {
    try {
      const job = this.distributedTrainingService.getTrainingJobStatus(jobId);
      
      if (!job) {
        throw new HttpException('训练任务不存在', HttpStatus.NOT_FOUND);
      }

      this.logger.log(`获取训练任务状态: ${jobId}`);
      return job;

    } catch (error) {
      this.logger.error(`获取训练任务状态失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取所有训练任务
   */
  @Get('jobs')
  @ApiOperation({ summary: '获取所有训练任务' })
  @ApiQuery({ name: 'status', required: false, description: '任务状态过滤' })
  @ApiQuery({ name: 'modelId', required: false, description: '模型ID过滤' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getTrainingJobs(
    @Query('status') status?: string,
    @Query('modelId') modelId?: string
  ): Promise<DistributedTrainingJob[]> {
    try {
      let jobs = this.distributedTrainingService.getTrainingJobs();
      
      if (status) {
        jobs = jobs.filter(job => job.status === status);
      }
      
      if (modelId) {
        jobs = jobs.filter(job => job.modelId === modelId);
      }

      this.logger.log(`获取训练任务列表: ${jobs.length} 个任务`);
      return jobs;

    } catch (error) {
      this.logger.error(`获取训练任务列表失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取训练统计信息
   */
  @Get('stats')
  @ApiOperation({ summary: '获取分布式训练统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getTrainingStats(): Promise<{
    totalNodes: number;
    activeNodes: number;
    totalJobs: number;
    activeJobs: number;
    completedJobs: number;
    failedJobs: number;
  }> {
    try {
      const nodes = this.distributedTrainingService.getTrainingNodes();
      const jobs = this.distributedTrainingService.getTrainingJobs();
      
      const stats = {
        totalNodes: nodes.length,
        activeNodes: nodes.filter(node => node.status === 'online' || node.status === 'training').length,
        totalJobs: jobs.length,
        activeJobs: jobs.filter(job => job.status === 'training' || job.status === 'initializing').length,
        completedJobs: jobs.filter(job => job.status === 'completed').length,
        failedJobs: jobs.filter(job => job.status === 'failed').length
      };

      this.logger.log('获取分布式训练统计信息');
      return stats;

    } catch (error) {
      this.logger.error(`获取分布式训练统计信息失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取节点性能指标
   */
  @Get('nodes/:nodeId/metrics')
  @ApiOperation({ summary: '获取节点性能指标' })
  @ApiParam({ name: 'nodeId', description: '节点ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '节点不存在' })
  async getNodeMetrics(@Param('nodeId') nodeId: string): Promise<any> {
    try {
      const nodes = this.distributedTrainingService.getTrainingNodes();
      const node = nodes.find(n => n.nodeId === nodeId);
      
      if (!node) {
        throw new HttpException('节点不存在', HttpStatus.NOT_FOUND);
      }

      const metrics = {
        nodeId: node.nodeId,
        status: node.status,
        currentLoad: node.currentLoad,
        performance: node.performance,
        capabilities: node.capabilities,
        lastHeartbeat: node.lastHeartbeat
      };

      this.logger.log(`获取节点性能指标: ${nodeId}`);
      return metrics;

    } catch (error) {
      this.logger.error(`获取节点性能指标失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取任务进度详情
   */
  @Get('jobs/:jobId/progress')
  @ApiOperation({ summary: '获取训练任务进度详情' })
  @ApiParam({ name: 'jobId', description: '训练任务ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '任务不存在' })
  async getJobProgress(@Param('jobId') jobId: string): Promise<any> {
    try {
      const job = this.distributedTrainingService.getTrainingJobStatus(jobId);
      
      if (!job) {
        throw new HttpException('训练任务不存在', HttpStatus.NOT_FOUND);
      }

      const progressDetails = {
        jobId: job.jobId,
        status: job.status,
        progress: job.progress,
        metrics: job.metrics,
        estimatedTimeRemaining: job.estimatedTimeRemaining,
        startTime: job.startTime,
        nodes: job.nodes.map(nodeId => {
          const nodeProgress = job.progress.nodesProgress[nodeId] || 0;
          const nodeMetrics = job.metrics.nodeMetrics[nodeId] || {};
          return {
            nodeId,
            progress: nodeProgress,
            metrics: nodeMetrics
          };
        })
      };

      this.logger.log(`获取任务进度详情: ${jobId}`);
      return progressDetails;

    } catch (error) {
      this.logger.error(`获取任务进度详情失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 取消训练任务
   */
  @Delete('jobs/:jobId')
  @ApiOperation({ summary: '取消训练任务' })
  @ApiParam({ name: 'jobId', description: '训练任务ID' })
  @ApiResponse({ status: 200, description: '取消成功' })
  @ApiResponse({ status: 404, description: '任务不存在' })
  async cancelTrainingJob(@Param('jobId') jobId: string): Promise<{ message: string }> {
    try {
      const job = this.distributedTrainingService.getTrainingJobStatus(jobId);
      
      if (!job) {
        throw new HttpException('训练任务不存在', HttpStatus.NOT_FOUND);
      }

      if (job.status === 'completed' || job.status === 'failed' || job.status === 'cancelled') {
        throw new HttpException('任务已完成或已取消，无法再次取消', HttpStatus.BAD_REQUEST);
      }

      // 更新任务状态为已取消
      job.status = 'cancelled';
      job.endTime = new Date();

      this.logger.log(`训练任务已取消: ${jobId}`);
      return { message: '训练任务已取消' };

    } catch (error) {
      this.logger.error(`取消训练任务失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
