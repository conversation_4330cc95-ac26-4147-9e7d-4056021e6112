version: '3.8'

services:
  voice-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: voice-service
    restart: unless-stopped
    ports:
      - "3010:3010"  # 微服务端口
      - "4010:4010"  # HTTP API端口
    environment:
      - NODE_ENV=production
      - VOICE_SERVICE_HOST=0.0.0.0
      - VOICE_SERVICE_PORT=3010
      - VOICE_HTTP_PORT=4010
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=1
    env_file:
      - .env
    volumes:
      - ./uploads:/app/uploads
      - ./temp:/app/temp
      - ./logs:/app/logs
    depends_on:
      - redis
    networks:
      - voice-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4010/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    container_name: voice-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - voice-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 可选：Redis管理界面
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: voice-redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    networks:
      - voice-network
    profiles:
      - tools

volumes:
  redis_data:
    driver: local

networks:
  voice-network:
    driver: bridge
