import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  Patch,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { VersionService } from './version.service';
import { CreateVersionDto, QueryVersionDto, CompareVersionDto } from './dto/version.dto';

@ApiTags('script-versions')
@Controller('scripts/:scriptId/versions')
// @UseGuards(JwtAuthGuard) // 需要实现认证守卫
export class VersionController {
  constructor(private readonly versionService: VersionService) {}

  @Post()
  @ApiOperation({ summary: '创建脚本版本' })
  @ApiParam({ name: 'scriptId', description: '脚本ID' })
  @ApiResponse({ status: HttpStatus.CREATED, description: '版本创建成功' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: '请求参数错误' })
  @ApiBearerAuth()
  async createVersion(
    @Param('scriptId') scriptId: string,
    @Body() createVersionDto: CreateVersionDto,
    @Request() req: any,
  ) {
    const userId = req.user?.id || 'anonymous';
    return this.versionService.createVersion(scriptId, createVersionDto, userId);
  }

  @Get()
  @ApiOperation({ summary: '获取脚本版本列表' })
  @ApiParam({ name: 'scriptId', description: '脚本ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  @ApiBearerAuth()
  async getVersions(
    @Param('scriptId') scriptId: string,
    @Query() queryDto: QueryVersionDto,
    @Request() req: any,
  ) {
    const userId = req.user?.id || 'anonymous';
    return this.versionService.getVersions(scriptId, queryDto, userId);
  }

  @Get(':versionId')
  @ApiOperation({ summary: '获取版本详情' })
  @ApiParam({ name: 'scriptId', description: '脚本ID' })
  @ApiParam({ name: 'versionId', description: '版本ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '版本不存在' })
  @ApiBearerAuth()
  async getVersion(
    @Param('scriptId') scriptId: string,
    @Param('versionId') versionId: string,
    @Request() req: any,
  ) {
    const userId = req.user?.id || 'anonymous';
    return this.versionService.getVersion(scriptId, versionId, userId);
  }

  @Delete(':versionId')
  @ApiOperation({ summary: '删除版本' })
  @ApiParam({ name: 'scriptId', description: '脚本ID' })
  @ApiParam({ name: 'versionId', description: '版本ID' })
  @ApiResponse({ status: HttpStatus.NO_CONTENT, description: '删除成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '版本不存在' })
  @ApiBearerAuth()
  async deleteVersion(
    @Param('scriptId') scriptId: string,
    @Param('versionId') versionId: string,
    @Request() req: any,
  ) {
    const userId = req.user?.id || 'anonymous';
    await this.versionService.deleteVersion(scriptId, versionId, userId);
  }

  @Post(':versionId/restore')
  @ApiOperation({ summary: '恢复到指定版本' })
  @ApiParam({ name: 'scriptId', description: '脚本ID' })
  @ApiParam({ name: 'versionId', description: '版本ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '恢复成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '版本不存在' })
  @ApiBearerAuth()
  async restoreVersion(
    @Param('scriptId') scriptId: string,
    @Param('versionId') versionId: string,
    @Request() req: any,
  ) {
    const userId = req.user?.id || 'anonymous';
    return this.versionService.restoreVersion(scriptId, versionId, userId);
  }

  @Post('compare')
  @ApiOperation({ summary: '比较版本' })
  @ApiParam({ name: 'scriptId', description: '脚本ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '比较成功' })
  @ApiBearerAuth()
  async compareVersions(
    @Param('scriptId') scriptId: string,
    @Body() compareDto: CompareVersionDto,
    @Request() req: any,
  ) {
    const userId = req.user?.id || 'anonymous';
    return this.versionService.compareVersions(scriptId, compareDto, userId);
  }

  @Get(':versionId/diff/:targetVersionId')
  @ApiOperation({ summary: '获取版本差异' })
  @ApiParam({ name: 'scriptId', description: '脚本ID' })
  @ApiParam({ name: 'versionId', description: '源版本ID' })
  @ApiParam({ name: 'targetVersionId', description: '目标版本ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  @ApiBearerAuth()
  async getVersionDiff(
    @Param('scriptId') scriptId: string,
    @Param('versionId') versionId: string,
    @Param('targetVersionId') targetVersionId: string,
    @Request() req: any,
  ) {
    const userId = req.user?.id || 'anonymous';
    return this.versionService.getVersionDiff(scriptId, versionId, targetVersionId, userId);
  }

  @Get('latest')
  @ApiOperation({ summary: '获取最新版本' })
  @ApiParam({ name: 'scriptId', description: '脚本ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  @ApiBearerAuth()
  async getLatestVersion(@Param('scriptId') scriptId: string, @Request() req: any) {
    const userId = req.user?.id || 'anonymous';
    return this.versionService.getLatestVersion(scriptId, userId);
  }
}
