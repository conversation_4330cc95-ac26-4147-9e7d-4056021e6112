import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { UIElement } from './ui-template.schema';

export type UITemplateVersionDocument = UITemplateVersion & Document;

// 版本类型枚举
export enum VersionType {
  MAJOR = 'major',
  MINOR = 'minor',
  PATCH = 'patch',
  SNAPSHOT = 'snapshot'
}

// 变更类型枚举
export enum ChangeType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  MOVE = 'move',
  RESIZE = 'resize',
  STYLE = 'style',
  PROPERTY = 'property',
  EVENT = 'event'
}

// 变更记录接口
export interface ChangeRecord {
  type: ChangeType;
  elementId: string;
  elementType: string;
  description: string;
  oldValue?: any;
  newValue?: any;
  timestamp: Date;
  userId: Types.ObjectId;
}

@Schema({ timestamps: true })
export class UITemplateVersion {
  @Prop({ required: true, type: Types.ObjectId, ref: 'UITemplate' })
  templateId: Types.ObjectId;

  @Prop({ required: true })
  version: string;

  @Prop({ required: true, enum: VersionType })
  versionType: VersionType;

  @Prop({ trim: true })
  description: string;

  @Prop({ type: Object, required: true })
  elements: UIElement[];

  @Prop({ type: Object })
  metadata: {
    canvasSize: { width: number; height: number };
    gridSize: number;
    snapToGrid: boolean;
    backgroundColor: string;
    backgroundImage?: string;
    responsive: boolean;
    breakpoints?: Record<string, number>;
  };

  @Prop({ type: [Object] })
  changes: ChangeRecord[];

  @Prop({ required: true, type: Types.ObjectId, ref: 'User' })
  createdBy: Types.ObjectId;

  @Prop({ type: String })
  parentVersion: string;

  @Prop([String])
  childVersions: string[];

  @Prop({ type: String })
  branchName: string;

  @Prop({ type: Object })
  diff: {
    added: UIElement[];
    modified: UIElement[];
    removed: UIElement[];
  };

  @Prop({ type: Object })
  statistics: {
    elementCount: number;
    complexity: number;
    size: number;
  };

  @Prop({ type: Boolean, default: false })
  isSnapshot: boolean;

  @Prop({ type: Boolean, default: false })
  isTagged: boolean;

  @Prop({ type: String })
  tag: string;

  @Prop({ type: Date })
  snapshotAt: Date;

  @Prop({ type: Date })
  taggedAt: Date;

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;

  @Prop({ type: Date })
  deletedAt: Date;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  deletedBy: Types.ObjectId;
}

export const UITemplateVersionSchema = SchemaFactory.createForClass(UITemplateVersion);

// 创建索引
UITemplateVersionSchema.index({ templateId: 1, version: 1 }, { unique: true });
UITemplateVersionSchema.index({ templateId: 1, createdAt: -1 });
UITemplateVersionSchema.index({ createdBy: 1, createdAt: -1 });
UITemplateVersionSchema.index({ versionType: 1 });
UITemplateVersionSchema.index({ isSnapshot: 1 });
UITemplateVersionSchema.index({ isTagged: 1 });
UITemplateVersionSchema.index({ isDeleted: 1 });

// 虚拟字段
UITemplateVersionSchema.virtual('id').get(function() {
  return this._id.toHexString();
});

UITemplateVersionSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret._id;
    delete ret.__v;
    return ret;
  }
});

// 中间件
UITemplateVersionSchema.pre('save', function(next) {
  if (this.isNew) {
    // 计算统计信息
    this.statistics = {
      elementCount: this.elements ? this.elements.length : 0,
      complexity: 0, // 将在后续计算
      size: JSON.stringify(this.elements).length
    };

    // 如果是快照，设置快照时间
    if (this.isSnapshot) {
      this.snapshotAt = new Date();
    }

    // 如果是标签版本，设置标签时间
    if (this.isTagged) {
      this.taggedAt = new Date();
    }
  }
  next();
});

UITemplateVersionSchema.pre('find', function() {
  this.where({ isDeleted: { $ne: true } });
});

UITemplateVersionSchema.pre('findOne', function() {
  this.where({ isDeleted: { $ne: true } });
});

UITemplateVersionSchema.pre('findOneAndUpdate', function() {
  this.where({ isDeleted: { $ne: true } });
});

UITemplateVersionSchema.pre('countDocuments', function() {
  this.where({ isDeleted: { $ne: true } });
});

// 实例方法
UITemplateVersionSchema.methods.calculateComplexity = function(): number {
  if (!this.elements || this.elements.length === 0) {
    return 0;
  }

  let complexity = 0;
  
  const calculateElementComplexity = (element: UIElement): number => {
    let elementComplexity = 1; // 基础复杂度

    // 根据属性数量增加复杂度
    if (element.properties) {
      elementComplexity += Object.keys(element.properties).length * 0.1;
    }

    // 根据样式数量增加复杂度
    if (element.style) {
      elementComplexity += Object.keys(element.style).length * 0.1;
    }

    // 根据事件数量增加复杂度
    if (element.events) {
      elementComplexity += Object.keys(element.events).length * 0.2;
    }

    // 递归计算子元素复杂度
    if (element.children && element.children.length > 0) {
      element.children.forEach(child => {
        elementComplexity += calculateElementComplexity(child);
      });
    }

    return elementComplexity;
  };

  this.elements.forEach((element: UIElement) => {
    complexity += calculateElementComplexity(element);
  });

  return Math.round(complexity * 100) / 100;
};

UITemplateVersionSchema.methods.softDelete = function(userId: Types.ObjectId) {
  this.isDeleted = true;
  this.deletedAt = new Date();
  this.deletedBy = userId;
  return this.save();
};

UITemplateVersionSchema.methods.restore = function() {
  this.isDeleted = false;
  this.deletedAt = undefined;
  this.deletedBy = undefined;
  return this.save();
};

UITemplateVersionSchema.methods.createSnapshot = function(description?: string) {
  const snapshot = new this.constructor({
    templateId: this.templateId,
    version: `${this.version}-snapshot-${Date.now()}`,
    versionType: VersionType.SNAPSHOT,
    description: description || `Snapshot of ${this.version}`,
    elements: this.elements,
    metadata: this.metadata,
    createdBy: this.createdBy,
    parentVersion: this.version,
    isSnapshot: true
  });

  return snapshot.save();
};

UITemplateVersionSchema.methods.createTag = function(tagName: string, description?: string) {
  this.isTagged = true;
  this.tag = tagName;
  this.taggedAt = new Date();
  if (description) {
    this.description = description;
  }
  return this.save();
};

UITemplateVersionSchema.methods.compareWith = function(otherVersion: UITemplateVersionDocument) {
  const thisElements = this.elements || [];
  const otherElements = otherVersion.elements || [];

  const thisElementMap = new Map(thisElements.map(el => [el.id, el]));
  const otherElementMap = new Map(otherElements.map(el => [el.id, el]));

  const added: UIElement[] = [];
  const modified: UIElement[] = [];
  const removed: UIElement[] = [];

  // 查找新增和修改的元素
  otherElements.forEach(element => {
    if (!thisElementMap.has(element.id)) {
      added.push(element);
    } else {
      const thisElement = thisElementMap.get(element.id);
      if (JSON.stringify(thisElement) !== JSON.stringify(element)) {
        modified.push(element);
      }
    }
  });

  // 查找删除的元素
  thisElements.forEach(element => {
    if (!otherElementMap.has(element.id)) {
      removed.push(element);
    }
  });

  return { added, modified, removed };
};

UITemplateVersionSchema.methods.generateDiff = function(previousVersion?: UITemplateVersionDocument) {
  if (!previousVersion) {
    this.diff = {
      added: this.elements || [],
      modified: [],
      removed: []
    };
  } else {
    this.diff = { added: [], modified: [], removed: [] }; // 简化处理
  }
  return this.save();
};

// 静态方法
UITemplateVersionSchema.statics.findByTemplate = function(templateId: Types.ObjectId, options: any = {}) {
  return this.find({ templateId, ...options }).sort({ createdAt: -1 });
};

UITemplateVersionSchema.statics.findLatestVersion = function(templateId: Types.ObjectId) {
  return this.findOne({ templateId, isSnapshot: false }).sort({ createdAt: -1 });
};

// 添加缺失的实例方法
UITemplateVersionSchema.methods.calculateComplexity = function(): number {
  if (!this.elements || !Array.isArray(this.elements)) {
    return 0;
  }

  let complexity = 0;

  // 基于元素数量计算复杂度
  complexity += this.elements.length;

  // 基于嵌套层级计算复杂度
  const calculateNesting = (elements: any[]): number => {
    let maxDepth = 0;
    for (const element of elements) {
      if (element.children && Array.isArray(element.children)) {
        maxDepth = Math.max(maxDepth, 1 + calculateNesting(element.children));
      }
    }
    return maxDepth;
  };

  complexity += calculateNesting(this.elements) * 2;

  // 基于交互元素数量计算复杂度
  const interactiveElements = this.elements.filter(el =>
    ['button', 'input', 'select', 'textarea', 'form'].includes(el.type)
  );
  complexity += interactiveElements.length * 1.5;

  return Math.round(complexity);
};

UITemplateVersionSchema.methods.compareWith = function(otherVersion: UITemplateVersionDocument) {
  const thisElements = this.elements || [];
  const otherElements = otherVersion.elements || [];

  const added = otherElements.filter(el =>
    !thisElements.find(thisEl => thisEl.id === el.id)
  );

  const removed = thisElements.filter(el =>
    !otherElements.find(otherEl => otherEl.id === el.id)
  );

  const modified = otherElements.filter(el => {
    const thisEl = thisElements.find(thisEl => thisEl.id === el.id);
    return thisEl && JSON.stringify(thisEl) !== JSON.stringify(el);
  });

  return {
    added,
    modified,
    removed
  };
};

UITemplateVersionSchema.statics.findByVersion = function(templateId: Types.ObjectId, version: string) {
  return this.findOne({ templateId, version });
};

UITemplateVersionSchema.statics.findSnapshots = function(templateId: Types.ObjectId) {
  return this.find({ templateId, isSnapshot: true }).sort({ createdAt: -1 });
};

UITemplateVersionSchema.statics.findTaggedVersions = function(templateId: Types.ObjectId) {
  return this.find({ templateId, isTagged: true }).sort({ createdAt: -1 });
};

UITemplateVersionSchema.statics.findByBranch = function(templateId: Types.ObjectId, branchName: string) {
  return this.find({ templateId, branchName }).sort({ createdAt: -1 });
};

UITemplateVersionSchema.statics.getVersionHistory = function(templateId: Types.ObjectId, limit: number = 50) {
  return this.find({ templateId })
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate('createdBy', 'name email avatar');
};
