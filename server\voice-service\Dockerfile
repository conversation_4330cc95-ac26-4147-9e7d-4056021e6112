# 使用官方Node.js运行时作为基础镜像
FROM node:18-alpine AS base

# 安装FFmpeg和其他必要的系统依赖
RUN apk add --no-cache \
    ffmpeg \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 开发阶段
FROM base AS development
RUN npm ci
COPY . .
RUN npm run build

# 生产阶段
FROM base AS production

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# 复制构建产物
COPY --from=development --chown=nestjs:nodejs /app/dist ./dist
COPY --from=development --chown=nestjs:nodejs /app/node_modules ./node_modules

# 创建必要的目录
RUN mkdir -p uploads/voice temp logs && \
    chown -R nestjs:nodejs uploads temp logs

# 切换到非root用户
USER nestjs

# 暴露端口
EXPOSE 3010 4010

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:4010/health || exit 1

# 启动应用
CMD ["node", "dist/main"]
