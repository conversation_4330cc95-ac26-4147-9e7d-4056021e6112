import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HealthIndicatorResult, HealthIndicator } from '@nestjs/terminus';
import * as Redis from 'redis';

@Injectable()
export class HealthService extends HealthIndicator {
  private redisClient: Redis.RedisClientType;

  constructor(private configService: ConfigService) {
    super();
    this.initializeRedis();
  }

  private async initializeRedis() {
    try {
      this.redisClient = Redis.createClient({
        url: `redis://${this.configService.get<string>('REDIS_HOST', 'localhost')}:${this.configService.get<number>('REDIS_PORT', 6379)}`,
        password: this.configService.get<string>('REDIS_PASSWORD'),
        database: this.configService.get<number>('REDIS_DB', 1),
      });

      await this.redisClient.connect();
    } catch (error) {
      console.warn('Redis连接失败:', error.message);
    }
  }

  async checkRedis(key: string): Promise<HealthIndicatorResult> {
    try {
      if (!this.redisClient || !this.redisClient.isOpen) {
        throw new Error('Redis连接未建立');
      }

      const start = Date.now();
      await this.redisClient.ping();
      const responseTime = Date.now() - start;

      const result = this.getStatus(key, true, {
        status: 'connected',
        responseTime: `${responseTime}ms`,
      });

      return result;
    } catch (error) {
      const result = this.getStatus(key, false, {
        status: 'disconnected',
        error: error.message,
      });

      return result;
    }
  }

  async checkVoiceServices(key: string): Promise<HealthIndicatorResult> {
    try {
      const services = {
        azure: await this.checkAzureService(),
        openai: await this.checkOpenAIService(),
        ffmpeg: await this.checkFFmpegService(),
      };

      const allHealthy = Object.values(services).every(service => service.healthy);

      const result = this.getStatus(key, allHealthy, {
        services,
        overall: allHealthy ? 'healthy' : 'degraded',
      });

      return result;
    } catch (error) {
      const result = this.getStatus(key, false, {
        error: error.message,
        overall: 'unhealthy',
      });

      return result;
    }
  }

  private async checkAzureService(): Promise<{ healthy: boolean; details?: any }> {
    try {
      const azureKey = this.configService.get<string>('AZURE_SPEECH_KEY');
      const azureRegion = this.configService.get<string>('AZURE_SPEECH_REGION');

      if (!azureKey || !azureRegion) {
        return {
          healthy: false,
          details: { error: 'Azure配置缺失' },
        };
      }

      // 这里可以添加实际的Azure服务检查
      return {
        healthy: true,
        details: { status: 'configured' },
      };
    } catch (error) {
      return {
        healthy: false,
        details: { error: error.message },
      };
    }
  }

  private async checkOpenAIService(): Promise<{ healthy: boolean; details?: any }> {
    try {
      const openaiKey = this.configService.get<string>('OPENAI_API_KEY');

      if (!openaiKey) {
        return {
          healthy: false,
          details: { error: 'OpenAI配置缺失' },
        };
      }

      // 这里可以添加实际的OpenAI服务检查
      return {
        healthy: true,
        details: { status: 'configured' },
      };
    } catch (error) {
      return {
        healthy: false,
        details: { error: error.message },
      };
    }
  }

  private async checkFFmpegService(): Promise<{ healthy: boolean; details?: any }> {
    try {
      // 检查FFmpeg是否可用
      const { exec } = require('child_process');
      
      return new Promise((resolve) => {
        exec('ffmpeg -version', (error, stdout, stderr) => {
          if (error) {
            resolve({
              healthy: false,
              details: { error: 'FFmpeg不可用' },
            });
          } else {
            const version = stdout.split('\n')[0];
            resolve({
              healthy: true,
              details: { version },
            });
          }
        });
      });
    } catch (error) {
      return {
        healthy: false,
        details: { error: error.message },
      };
    }
  }

  async getDetailedHealthInfo() {
    const info = {
      timestamp: new Date().toISOString(),
      service: 'voice-service',
      version: '1.0.0',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      environment: process.env.NODE_ENV,
      redis: await this.getRedisInfo(),
      voiceServices: await this.getVoiceServicesInfo(),
    };

    return info;
  }

  private async getRedisInfo() {
    try {
      if (!this.redisClient || !this.redisClient.isOpen) {
        return { status: 'disconnected' };
      }

      const info = await this.redisClient.info();
      return {
        status: 'connected',
        info: info.split('\n').slice(0, 5).join('\n'), // 只返回前几行信息
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
      };
    }
  }

  private async getVoiceServicesInfo() {
    return {
      azure: {
        configured: !!(this.configService.get('AZURE_SPEECH_KEY') && this.configService.get('AZURE_SPEECH_REGION')),
      },
      openai: {
        configured: !!this.configService.get('OPENAI_API_KEY'),
      },
      google: {
        configured: !!this.configService.get('GOOGLE_APPLICATION_CREDENTIALS'),
      },
      baidu: {
        configured: !!(this.configService.get('BAIDU_APP_ID') && this.configService.get('BAIDU_API_KEY')),
      },
      tencent: {
        configured: !!(this.configService.get('TENCENT_SECRET_ID') && this.configService.get('TENCENT_SECRET_KEY')),
      },
    };
  }

  async onModuleDestroy() {
    if (this.redisClient && this.redisClient.isOpen) {
      await this.redisClient.disconnect();
    }
  }
}
