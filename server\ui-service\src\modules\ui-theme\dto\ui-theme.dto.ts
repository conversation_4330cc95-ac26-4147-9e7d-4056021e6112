import { IsString, IsEnum, IsOptional, IsObject, IsBoolean, IsArray, IsMongoId, MaxLength, IsNumber, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { ThemeType, ThemeStatus, ColorConfig, FontConfig, SpacingConfig, BorderRadiusConfig, ShadowConfig, TransitionConfig } from '../schemas/ui-theme.schema';

export class CreateUIThemeDto {
  @ApiProperty({ description: '主题名称', maxLength: 100 })
  @IsString()
  @MaxLength(100)
  name: string;

  @ApiPropertyOptional({ description: '主题描述', maxLength: 500 })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiProperty({ description: '主题类型', enum: ThemeType })
  @IsEnum(ThemeType)
  type: ThemeType;

  @ApiProperty({ description: '颜色配置', type: 'object' })
  @IsObject()
  colors: ColorConfig;

  @ApiProperty({ description: '字体配置', type: 'object' })
  @IsObject()
  fonts: FontConfig;

  @ApiProperty({ description: '间距配置', type: 'object' })
  @IsObject()
  spacing: SpacingConfig;

  @ApiProperty({ description: '圆角配置', type: 'object' })
  @IsObject()
  borderRadius: BorderRadiusConfig;

  @ApiProperty({ description: '阴影配置', type: 'object' })
  @IsObject()
  shadows: ShadowConfig;

  @ApiProperty({ description: '过渡动画配置', type: 'object' })
  @IsObject()
  transitions: TransitionConfig;

  @ApiPropertyOptional({ description: '标签', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: '元数据', type: 'object' })
  @IsOptional()
  @IsObject()
  metadata?: any;

  @ApiPropertyOptional({ description: '组织ID' })
  @IsOptional()
  @IsMongoId()
  organizationId?: string;

  @ApiPropertyOptional({ description: '是否公开', default: true })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;
}

export class UpdateUIThemeDto {
  @ApiPropertyOptional({ description: '主题名称', maxLength: 100 })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  name?: string;

  @ApiPropertyOptional({ description: '主题描述', maxLength: 500 })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiPropertyOptional({ description: '主题状态', enum: ThemeStatus })
  @IsOptional()
  @IsEnum(ThemeStatus)
  status?: ThemeStatus;

  @ApiPropertyOptional({ description: '颜色配置', type: 'object' })
  @IsOptional()
  @IsObject()
  colors?: ColorConfig;

  @ApiPropertyOptional({ description: '字体配置', type: 'object' })
  @IsOptional()
  @IsObject()
  fonts?: FontConfig;

  @ApiPropertyOptional({ description: '间距配置', type: 'object' })
  @IsOptional()
  @IsObject()
  spacing?: SpacingConfig;

  @ApiPropertyOptional({ description: '圆角配置', type: 'object' })
  @IsOptional()
  @IsObject()
  borderRadius?: BorderRadiusConfig;

  @ApiPropertyOptional({ description: '阴影配置', type: 'object' })
  @IsOptional()
  @IsObject()
  shadows?: ShadowConfig;

  @ApiPropertyOptional({ description: '过渡动画配置', type: 'object' })
  @IsOptional()
  @IsObject()
  transitions?: TransitionConfig;

  @ApiPropertyOptional({ description: '标签', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: '元数据', type: 'object' })
  @IsOptional()
  @IsObject()
  metadata?: any;

  @ApiPropertyOptional({ description: '是否公开' })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;

  @ApiPropertyOptional({ description: '是否激活' })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class QueryUIThemeDto {
  @ApiPropertyOptional({ description: '页码', minimum: 1, default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100, default: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({ description: '搜索关键词' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: '主题类型', enum: ThemeType })
  @IsOptional()
  @IsEnum(ThemeType)
  type?: ThemeType;

  @ApiPropertyOptional({ description: '主题状态', enum: ThemeStatus })
  @IsOptional()
  @IsEnum(ThemeStatus)
  status?: ThemeStatus;

  @ApiPropertyOptional({ description: '组织ID' })
  @IsOptional()
  @IsMongoId()
  organizationId?: string;

  @ApiPropertyOptional({ description: '标签' })
  @IsOptional()
  @IsString()
  tags?: string;

  @ApiPropertyOptional({ description: '是否公开' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  isPublic?: boolean;

  @ApiPropertyOptional({ description: '排序字段', default: 'createdAt' })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ description: '排序方向', enum: ['asc', 'desc'], default: 'desc' })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc';
}
