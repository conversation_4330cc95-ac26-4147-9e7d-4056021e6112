import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { Socket } from 'socket.io';

@Injectable()
export class WsJwtGuard implements CanActivate {
  private readonly logger = new Logger(WsJwtGuard.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const client: Socket = context.switchToWs().getClient();
    const token = this.extractTokenFromSocket(client);

    if (!token) {
      this.logger.warn(`WebSocket连接缺少认证令牌: ${client.id}`);
      client.disconnect();
      return false;
    }

    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: this.configService.get<string>('JWT_SECRET'),
      });

      // 将用户信息附加到socket对象
      client.handshake.auth.user = payload;
      client.handshake.auth.userId = payload.sub || payload.id;
      
      return true;
    } catch (error) {
      this.logger.warn(`WebSocket令牌验证失败: ${client.id}, ${error.message}`);
      client.disconnect();
      return false;
    }
  }

  private extractTokenFromSocket(client: Socket): string | undefined {
    // 从查询参数中获取token
    const tokenFromQuery = client.handshake.query?.token as string;
    if (tokenFromQuery) {
      return tokenFromQuery;
    }

    // 从认证头中获取token
    const authHeader = client.handshake.headers?.authorization;
    if (authHeader) {
      const [type, token] = authHeader.split(' ');
      return type === 'Bearer' ? token : undefined;
    }

    // 从认证对象中获取token
    return client.handshake.auth?.token;
  }
}
