import { Modu<PERSON> } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { AIEngineService } from './ai-engine.service';
import { AIEngineController } from './ai-engine.controller';
import { DistributedTrainingService } from './distributed-training.service';
import { DistributedTrainingController } from './distributed-training.controller';
import { ModelOptimizationService } from './model-optimization.service';
import { ModelOptimizationController } from './model-optimization.controller';
import { ModelDeploymentService } from './model-deployment.service';

@Module({
  imports: [
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),
  ],
  controllers: [
    AIEngineController,
    DistributedTrainingController,
    ModelOptimizationController
  ],
  providers: [
    AIEngineService,
    DistributedTrainingService,
    ModelOptimizationService,
    ModelDeploymentService
  ],
  exports: [
    AIEngineService,
    DistributedTrainingService,
    ModelOptimizationService,
    ModelDeploymentService
  ],
})
export class AIEngineModule {}
