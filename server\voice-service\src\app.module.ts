import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { MulterModule } from '@nestjs/platform-express';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { diskStorage } from 'multer';
import * as path from 'path';
import * as fs from 'fs-extra';

import { VoiceModule } from './voice/voice.module';
import { SpeechRecognitionModule } from './speech-recognition/speech-recognition.module';
import { SpeechSynthesisModule } from './speech-synthesis/speech-synthesis.module';
import { AudioProcessingModule } from './audio-processing/audio-processing.module';
import { LipSyncModule } from './lip-sync/lip-sync.module';
import { HealthModule } from './health/health.module';

// 过滤器和拦截器
import { HttpExceptionFilter, AllExceptionsFilter } from './common/filters/http-exception.filter';
import { LoggingInterceptor, TransformInterceptor } from './common/interceptors/logging.interceptor';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // JWT模块
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '24h'),
        },
      }),
      global: true,
    }),

    // Passport模块
    PassportModule.register({ defaultStrategy: 'jwt' }),

    // 事件发射器模块
    EventEmitterModule.forRoot({
      wildcard: true,
      delimiter: '.',
      maxListeners: 20,
      verboseMemoryLeak: true,
    }),



    // 文件上传模块
    MulterModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        storage: diskStorage({
          destination: (req, file, cb) => {
            const uploadPath = configService.get<string>('UPLOAD_PATH', './uploads/voice');
            
            // 确保目录存在
            fs.ensureDirSync(uploadPath);
            
            cb(null, uploadPath);
          },
          filename: (req, file, cb) => {
            // 生成唯一文件名
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const ext = path.extname(file.originalname);
            cb(null, `${file.fieldname}-${uniqueSuffix}${ext}`);
          },
        }),
        limits: {
          fileSize: configService.get<number>('MAX_AUDIO_FILE_SIZE', 100 * 1024 * 1024), // 100MB
        },
        fileFilter: (req, file, cb) => {
          // 允许的音频文件类型
          const allowedMimes = [
            'audio/wav',
            'audio/wave',
            'audio/x-wav',
            'audio/mpeg',
            'audio/mp3',
            'audio/flac',
            'audio/ogg',
            'audio/webm',
            'audio/aac',
            'audio/m4a',
            'audio/x-m4a',
          ];
          
          if (allowedMimes.includes(file.mimetype)) {
            cb(null, true);
          } else {
            cb(new Error(`不支持的音频文件类型: ${file.mimetype}`), false);
          }
        },
      }),
    }),

    // 业务模块
    VoiceModule,
    SpeechRecognitionModule,
    SpeechSynthesisModule,
    AudioProcessingModule,
    LipSyncModule,
    HealthModule,
  ],
  providers: [
    // 全局异常过滤器
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
    // 全局拦截器
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TransformInterceptor,
    },
  ],
})
export class AppModule {}
