import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { VoiceService } from './voice.service';
import { SpeechRecognitionService } from '../speech-recognition/speech-recognition.service';
import { SpeechSynthesisService } from '../speech-synthesis/speech-synthesis.service';
import { AudioProcessingService } from '../audio-processing/audio-processing.service';
import { LipSyncService } from '../lip-sync/lip-sync.service';

describe('VoiceService', () => {
  let service: VoiceService;
  let speechRecognitionService: SpeechRecognitionService;
  let speechSynthesisService: SpeechSynthesisService;
  let audioProcessingService: AudioProcessingService;
  let lipSyncService: LipSyncService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VoiceService,
        {
          provide: SpeechRecognitionService,
          useValue: {
            recognizeAudioFile: jest.fn(),
            startRealtimeRecognition: jest.fn(),
            stopRealtimeRecognition: jest.fn(),
            batchRecognize: jest.fn(),
            getSupportedLanguages: jest.fn(),
            getServiceStatistics: jest.fn(),
          },
        },
        {
          provide: SpeechSynthesisService,
          useValue: {
            synthesizeSpeech: jest.fn(),
            getAvailableVoices: jest.fn(),
            previewVoice: jest.fn(),
            batchSynthesize: jest.fn(),
            clearCache: jest.fn(),
            getServiceStatistics: jest.fn(),
          },
        },
        {
          provide: AudioProcessingService,
          useValue: {
            convertAudio: jest.fn(),
            analyzeAudio: jest.fn(),
            getSupportedFormats: jest.fn(),
          },
        },
        {
          provide: LipSyncService,
          useValue: {
            generateLipSync: jest.fn(),
            getSupportedLanguages: jest.fn(),
            getSupportedVisemes: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: EventEmitter2,
          useValue: {
            emit: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<VoiceService>(VoiceService);
    speechRecognitionService = module.get<SpeechRecognitionService>(SpeechRecognitionService);
    speechSynthesisService = module.get<SpeechSynthesisService>(SpeechSynthesisService);
    audioProcessingService = module.get<AudioProcessingService>(AudioProcessingService);
    lipSyncService = module.get<LipSyncService>(LipSyncService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('recognizeSpeech', () => {
    it('should recognize speech and emit event', async () => {
      const mockResult = {
        id: 'test-id',
        text: '测试文本',
        confidence: 0.9,
        language: 'zh-CN',
        duration: 3.0,
        provider: 'azure',
        processingTime: 1000,
      };

      jest.spyOn(speechRecognitionService, 'recognizeAudioFile').mockResolvedValue(mockResult);

      const audioBuffer = Buffer.from('test audio data');
      const config = {
        provider: 'azure' as any,
        language: 'zh-CN',
      };

      const result = await service.recognizeSpeech(audioBuffer, config);

      expect(result).toEqual(mockResult);
      expect(speechRecognitionService.recognizeAudioFile).toHaveBeenCalledWith(audioBuffer, config);
    });
  });

  describe('synthesizeSpeech', () => {
    it('should synthesize speech and emit event', async () => {
      const mockResult = {
        id: 'test-id',
        audioData: Buffer.from('test audio'),
        duration: 3.0,
        format: 'wav',
        sampleRate: 16000,
        channels: 1,
        voice: 'zh-CN-XiaoxiaoNeural',
        language: 'zh-CN',
        provider: 'azure' as any,
        processingTime: 1000,
      };

      jest.spyOn(speechSynthesisService, 'synthesizeSpeech').mockResolvedValue(mockResult);

      const text = '测试文本';
      const config = {
        provider: 'azure' as any,
        voice: 'zh-CN-XiaoxiaoNeural',
        language: 'zh-CN',
      };

      const result = await service.synthesizeSpeech(text, config);

      expect(result).toEqual(mockResult);
      expect(speechSynthesisService.synthesizeSpeech).toHaveBeenCalledWith(text, config);
    });
  });

  describe('processAudio', () => {
    it('should process audio', async () => {
      const mockResult = Buffer.from('processed audio');
      jest.spyOn(audioProcessingService, 'convertAudio').mockResolvedValue(mockResult);

      const audioBuffer = Buffer.from('test audio');
      const config = {
        outputFormat: 'wav' as any,
      };

      const result = await service.processAudio(audioBuffer, config);

      expect(result).toEqual(mockResult);
      expect(audioProcessingService.convertAudio).toHaveBeenCalledWith(audioBuffer, config);
    });
  });

  describe('generateLipSync', () => {
    it('should generate lip sync data', async () => {
      const mockResult = {
        id: 'test-id',
        duration: 3.0,
        keyframes: [],
        sampleRate: 30,
        language: 'zh-CN',
        method: 'phoneme' as any,
        confidence: 0.8,
      };

      jest.spyOn(lipSyncService, 'generateLipSync').mockResolvedValue(mockResult);

      const audioBuffer = Buffer.from('test audio');
      const text = '测试文本';
      const config = {
        method: 'phoneme' as any,
        language: 'zh-CN',
        frameRate: 30,
        smoothing: 0.5,
        intensity: 1.0,
      };

      const result = await service.generateLipSync(audioBuffer, text, config);

      expect(result).toEqual(mockResult);
      expect(lipSyncService.generateLipSync).toHaveBeenCalledWith(audioBuffer, text, config);
    });
  });

  describe('getStatistics', () => {
    it('should return service statistics', () => {
      const mockStats = {
        speechRecognition: { activeRealtimeSessions: 0 },
        speechSynthesis: { totalSynthesized: 0 },
        audioProcessing: { supportedFormats: [] },
        lipSync: { supportedLanguages: [], supportedVisemes: [] },
      };

      jest.spyOn(speechRecognitionService, 'getServiceStatistics').mockReturnValue(mockStats.speechRecognition);
      jest.spyOn(speechSynthesisService, 'getServiceStatistics').mockReturnValue(mockStats.speechSynthesis);
      jest.spyOn(audioProcessingService, 'getSupportedFormats').mockReturnValue(mockStats.audioProcessing.supportedFormats);
      jest.spyOn(lipSyncService, 'getSupportedLanguages').mockReturnValue(mockStats.lipSync.supportedLanguages);
      jest.spyOn(lipSyncService, 'getSupportedVisemes').mockReturnValue(mockStats.lipSync.supportedVisemes);

      const result = service.getStatistics();

      expect(result).toEqual({
        speechRecognition: mockStats.speechRecognition,
        speechSynthesis: mockStats.speechSynthesis,
        audioProcessing: mockStats.audioProcessing.supportedFormats,
        lipSync: {
          supportedLanguages: mockStats.lipSync.supportedLanguages,
          supportedVisemes: mockStats.lipSync.supportedVisemes,
        },
      });
    });
  });
});
