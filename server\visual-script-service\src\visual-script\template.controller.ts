import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  Patch,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { TemplateService } from './template.service';
import { CreateTemplateDto, UpdateTemplateDto, QueryTemplateDto } from './dto/template.dto';

@ApiTags('script-templates')
@Controller('templates')
// @UseGuards(JwtAuthGuard) // 需要实现认证守卫
export class TemplateController {
  constructor(private readonly templateService: TemplateService) {}

  @Post()
  @ApiOperation({ summary: '创建脚本模板' })
  @ApiResponse({ status: HttpStatus.CREATED, description: '模板创建成功' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: '请求参数错误' })
  @ApiBearerAuth()
  async createTemplate(@Body() createTemplateDto: CreateTemplateDto, @Request() req: any) {
    const userId = req.user?.id || 'anonymous';
    return this.templateService.createTemplate(createTemplateDto, userId);
  }

  @Get()
  @ApiOperation({ summary: '获取模板列表' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  async getTemplates(@Query() queryDto: QueryTemplateDto, @Request() req: any) {
    const userId = req.user?.id || 'anonymous';
    return this.templateService.getTemplates(queryDto, userId);
  }

  @Get('categories')
  @ApiOperation({ summary: '获取模板分类' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  async getTemplateCategories() {
    return this.templateService.getTemplateCategories();
  }

  @Get('popular')
  @ApiOperation({ summary: '获取热门模板' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  async getPopularTemplates(@Query() queryDto: QueryTemplateDto) {
    return this.templateService.getPopularTemplates(queryDto);
  }

  @Get(':templateId')
  @ApiOperation({ summary: '获取模板详情' })
  @ApiParam({ name: 'templateId', description: '模板ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '模板不存在' })
  async getTemplate(@Param('templateId') templateId: string, @Request() req: any) {
    const userId = req.user?.id || 'anonymous';
    return this.templateService.getTemplate(templateId, userId);
  }

  @Patch(':templateId')
  @ApiOperation({ summary: '更新模板' })
  @ApiParam({ name: 'templateId', description: '模板ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '更新成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '模板不存在' })
  @ApiBearerAuth()
  async updateTemplate(
    @Param('templateId') templateId: string,
    @Body() updateTemplateDto: UpdateTemplateDto,
    @Request() req: any,
  ) {
    const userId = req.user?.id || 'anonymous';
    return this.templateService.updateTemplate(templateId, updateTemplateDto, userId);
  }

  @Delete(':templateId')
  @ApiOperation({ summary: '删除模板' })
  @ApiParam({ name: 'templateId', description: '模板ID' })
  @ApiResponse({ status: HttpStatus.NO_CONTENT, description: '删除成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '模板不存在' })
  @ApiBearerAuth()
  async deleteTemplate(@Param('templateId') templateId: string, @Request() req: any) {
    const userId = req.user?.id || 'anonymous';
    await this.templateService.deleteTemplate(templateId, userId);
  }

  @Post(':templateId/use')
  @ApiOperation({ summary: '使用模板创建脚本' })
  @ApiParam({ name: 'templateId', description: '模板ID' })
  @ApiResponse({ status: HttpStatus.CREATED, description: '脚本创建成功' })
  @ApiBearerAuth()
  async useTemplate(
    @Param('templateId') templateId: string,
    @Body() body: { name?: string; description?: string },
    @Request() req: any,
  ) {
    const userId = req.user?.id || 'anonymous';
    return this.templateService.useTemplate(templateId, body, userId);
  }

  @Post(':templateId/clone')
  @ApiOperation({ summary: '克隆模板' })
  @ApiParam({ name: 'templateId', description: '模板ID' })
  @ApiResponse({ status: HttpStatus.CREATED, description: '克隆成功' })
  @ApiBearerAuth()
  async cloneTemplate(
    @Param('templateId') templateId: string,
    @Body() body: { name?: string; description?: string },
    @Request() req: any,
  ) {
    const userId = req.user?.id || 'anonymous';
    return this.templateService.cloneTemplate(templateId, body, userId);
  }

  @Post(':templateId/like')
  @ApiOperation({ summary: '点赞模板' })
  @ApiParam({ name: 'templateId', description: '模板ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '点赞成功' })
  @ApiBearerAuth()
  async likeTemplate(@Param('templateId') templateId: string, @Request() req: any) {
    const userId = req.user?.id || 'anonymous';
    return this.templateService.likeTemplate(templateId, userId);
  }

  @Delete(':templateId/like')
  @ApiOperation({ summary: '取消点赞模板' })
  @ApiParam({ name: 'templateId', description: '模板ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '取消点赞成功' })
  @ApiBearerAuth()
  async unlikeTemplate(@Param('templateId') templateId: string, @Request() req: any) {
    const userId = req.user?.id || 'anonymous';
    return this.templateService.unlikeTemplate(templateId, userId);
  }
}
