/**
 * Jest测试设置文件
 */

import { Test } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';

// 全局变量
declare global {
  var __DB_CONNECTION__: any;
}

/**
 * 全局设置 - 在所有测试开始前执行
 */
beforeAll(async () => {
  // 设置环境变量
  process.env.NODE_ENV = 'test';
  process.env.DB_HOST = 'localhost';
  process.env.DB_PORT = '3306';
  process.env.DB_USERNAME = 'test';
  process.env.DB_PASSWORD = 'test';
  process.env.DB_DATABASE = 'test_dlengine';
  process.env.REDIS_HOST = 'localhost';
  process.env.REDIS_PORT = '6379';
  process.env.JWT_SECRET = 'test-secret';
});

/**
 * 全局清理 - 在所有测试结束后执行
 */
afterAll(async () => {
  // 清理数据库连接
  if (global.__DB_CONNECTION__) {
    await global.__DB_CONNECTION__.close();
  }
});

/**
 * 每个测试前的设置
 */
beforeEach(() => {
  // 清除所有模拟
  jest.clearAllMocks();
});

/**
 * 创建测试模块的辅助函数
 */
export const createTestingModule = async (imports: any[] = [], providers: any[] = []) => {
  return Test.createTestingModule({
    imports: [
      TypeOrmModule.forRoot({
        type: 'mysql',
        host: process.env.DB_HOST,
        port: parseInt(process.env.DB_PORT || '3306'),
        username: process.env.DB_USERNAME,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_DATABASE,
        entities: [],
        synchronize: true,
        dropSchema: true,
      }),
      ...imports,
    ],
    providers,
  }).compile();
};

/**
 * 模拟Redis客户端
 */
export const mockRedisClient = {
  get: jest.fn(),
  set: jest.fn(),
  setex: jest.fn(),
  del: jest.fn(),
  keys: jest.fn(),
  ping: jest.fn().mockResolvedValue('PONG'),
  quit: jest.fn(),
  disconnect: jest.fn(),
};

/**
 * 模拟用户数据
 */
export const mockUser = {
  id: 'test-user-id',
  username: 'testuser',
  email: '<EMAIL>',
  roles: ['user'],
};

/**
 * 模拟请求对象
 */
export const mockRequest = {
  user: mockUser,
  headers: {
    authorization: 'Bearer mock-token',
  },
  ip: '127.0.0.1',
  get: jest.fn(),
};

/**
 * 模拟响应对象
 */
export const mockResponse = {
  status: jest.fn().mockReturnThis(),
  json: jest.fn().mockReturnThis(),
  send: jest.fn().mockReturnThis(),
  cookie: jest.fn().mockReturnThis(),
  clearCookie: jest.fn().mockReturnThis(),
};

/**
 * 等待异步操作完成的辅助函数
 */
export const waitForAsync = (ms = 0) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * 生成随机字符串
 */
export const randomString = (length = 10) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

/**
 * 生成随机数字
 */
export const randomNumber = (min = 0, max = 100) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

/**
 * 生成模拟UUID
 */
export const mockUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};
