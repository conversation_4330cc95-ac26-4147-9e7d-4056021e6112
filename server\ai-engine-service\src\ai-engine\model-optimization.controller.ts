/**
 * 模型优化控制器
 * 提供模型优化相关的API接口
 */

import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { 
  ModelOptimizationService, 
  OptimizationConfig, 
  OptimizationResult,
  OptimizationStrategy 
} from './model-optimization.service';

@ApiTags('模型优化')
@Controller('api/v1/model-optimization')
export class ModelOptimizationController {
  private readonly logger = new Logger(ModelOptimizationController.name);

  constructor(private readonly modelOptimizationService: ModelOptimizationService) {}

  /**
   * 优化模型
   */
  @Post('optimize')
  @ApiOperation({ summary: '优化模型' })
  @ApiResponse({ status: 201, description: '优化任务创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async optimizeModel(@Body() request: {
    modelId: string;
    config: OptimizationConfig;
  }): Promise<OptimizationResult> {
    try {
      this.logger.log(`开始优化模型: ${request.modelId}, 策略: ${request.config.strategy}`);
      
      const result = await this.modelOptimizationService.optimizeModel(
        request.modelId,
        request.config
      );
      
      this.logger.log(`模型优化完成: ${result.optimizationId}`);
      return result;

    } catch (error) {
      this.logger.error(`模型优化失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 获取优化结果
   */
  @Get('results/:optimizationId')
  @ApiOperation({ summary: '获取优化结果' })
  @ApiParam({ name: 'optimizationId', description: '优化任务ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '优化结果不存在' })
  async getOptimizationResult(@Param('optimizationId') optimizationId: string): Promise<OptimizationResult> {
    try {
      const result = this.modelOptimizationService.getOptimizationResult(optimizationId);
      
      if (!result) {
        throw new HttpException('优化结果不存在', HttpStatus.NOT_FOUND);
      }

      this.logger.log(`获取优化结果: ${optimizationId}`);
      return result;

    } catch (error) {
      this.logger.error(`获取优化结果失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取所有优化结果
   */
  @Get('results')
  @ApiOperation({ summary: '获取所有优化结果' })
  @ApiQuery({ name: 'strategy', required: false, description: '优化策略过滤' })
  @ApiQuery({ name: 'modelId', required: false, description: '模型ID过滤' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getAllOptimizationResults(
    @Query('strategy') strategy?: OptimizationStrategy,
    @Query('modelId') modelId?: string
  ): Promise<OptimizationResult[]> {
    try {
      let results = this.modelOptimizationService.getAllOptimizationResults();
      
      if (strategy) {
        results = results.filter(result => result.strategy === strategy);
      }
      
      if (modelId) {
        results = results.filter(result => result.originalModelId === modelId);
      }

      this.logger.log(`获取优化结果列表: ${results.length} 个结果`);
      return results;

    } catch (error) {
      this.logger.error(`获取优化结果列表失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 比较优化结果
   */
  @Post('compare')
  @ApiOperation({ summary: '比较多个优化结果' })
  @ApiResponse({ status: 200, description: '比较成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async compareOptimizations(@Body() request: {
    optimizationIds: string[];
  }): Promise<{
    comparison: OptimizationResult[];
    bestBySize: OptimizationResult;
    bestBySpeed: OptimizationResult;
    bestByAccuracy: OptimizationResult;
  }> {
    try {
      this.logger.log(`比较优化结果: ${request.optimizationIds.join(', ')}`);
      
      const comparison = this.modelOptimizationService.compareOptimizations(request.optimizationIds);
      
      this.logger.log(`优化结果比较完成`);
      return comparison;

    } catch (error) {
      this.logger.error(`比较优化结果失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 获取优化策略列表
   */
  @Get('strategies')
  @ApiOperation({ summary: '获取支持的优化策略' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getOptimizationStrategies(): Promise<{
    strategies: {
      name: OptimizationStrategy;
      description: string;
      parameters: any;
    }[];
  }> {
    try {
      const strategies = [
        {
          name: OptimizationStrategy.QUANTIZATION,
          description: '模型量化 - 减少模型权重的精度以降低模型大小和提高推理速度',
          parameters: {
            quantizationBits: {
              type: 'number',
              description: '量化位数',
              default: 8,
              range: [1, 32]
            }
          }
        },
        {
          name: OptimizationStrategy.PRUNING,
          description: '模型剪枝 - 移除不重要的权重和连接以减少模型复杂度',
          parameters: {
            pruningRatio: {
              type: 'number',
              description: '剪枝比例',
              default: 0.5,
              range: [0.1, 0.9]
            }
          }
        },
        {
          name: OptimizationStrategy.DISTILLATION,
          description: '知识蒸馏 - 使用大模型训练小模型以保持性能的同时减少模型大小',
          parameters: {
            distillationTemperature: {
              type: 'number',
              description: '蒸馏温度',
              default: 3.0,
              range: [1.0, 10.0]
            }
          }
        },
        {
          name: OptimizationStrategy.COMPRESSION,
          description: '模型压缩 - 使用各种压缩技术减少模型存储空间',
          parameters: {
            compressionRatio: {
              type: 'number',
              description: '压缩比例',
              default: 0.5,
              range: [0.1, 0.9]
            }
          }
        },
        {
          name: OptimizationStrategy.MIXED_PRECISION,
          description: '混合精度 - 使用不同精度的数据类型优化训练和推理',
          parameters: {
            mixedPrecisionPolicy: {
              type: 'string',
              description: '混合精度策略',
              default: 'mixed_float16',
              options: ['mixed_float16', 'mixed_bfloat16']
            }
          }
        }
      ];

      this.logger.log('获取优化策略列表');
      return { strategies };

    } catch (error) {
      this.logger.error(`获取优化策略列表失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取优化建议
   */
  @Post('recommendations')
  @ApiOperation({ summary: '获取模型优化建议' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async getOptimizationRecommendations(@Body() request: {
    modelId: string;
    targetMetrics: {
      maxAccuracyLoss?: number;
      targetModelSize?: number;
      targetInferenceTime?: number;
    };
    constraints: {
      maxOptimizationTime?: number;
      availableStrategies?: OptimizationStrategy[];
    };
  }): Promise<{
    recommendations: {
      strategy: OptimizationStrategy;
      config: OptimizationConfig;
      expectedResults: {
        sizeReduction: number;
        speedup: number;
        accuracyLoss: number;
      };
      priority: number;
    }[];
  }> {
    try {
      this.logger.log(`获取模型优化建议: ${request.modelId}`);
      
      // 基于目标指标和约束生成优化建议
      const recommendations = this.generateOptimizationRecommendations(request);
      
      this.logger.log(`生成优化建议完成: ${recommendations.length} 个建议`);
      return { recommendations };

    } catch (error) {
      this.logger.error(`获取优化建议失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 获取优化统计信息
   */
  @Get('stats')
  @ApiOperation({ summary: '获取优化统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getOptimizationStats(): Promise<{
    totalOptimizations: number;
    strategiesUsage: { [strategy: string]: number };
    averageMetrics: {
      sizeReduction: number;
      speedup: number;
      accuracyLoss: number;
      optimizationTime: number;
    };
    bestResults: {
      bestSizeReduction: OptimizationResult;
      bestSpeedup: OptimizationResult;
      bestAccuracyPreservation: OptimizationResult;
    };
  }> {
    try {
      const results = this.modelOptimizationService.getAllOptimizationResults();
      
      if (results.length === 0) {
        return {
          totalOptimizations: 0,
          strategiesUsage: {},
          averageMetrics: {
            sizeReduction: 0,
            speedup: 0,
            accuracyLoss: 0,
            optimizationTime: 0
          },
          bestResults: {
            bestSizeReduction: null as any,
            bestSpeedup: null as any,
            bestAccuracyPreservation: null as any
          }
        };
      }

      // 计算策略使用统计
      const strategiesUsage: { [strategy: string]: number } = {};
      results.forEach(result => {
        strategiesUsage[result.strategy] = (strategiesUsage[result.strategy] || 0) + 1;
      });

      // 计算平均指标
      const averageMetrics = {
        sizeReduction: results.reduce((sum, r) => sum + r.metrics.sizeReduction, 0) / results.length,
        speedup: results.reduce((sum, r) => sum + r.metrics.speedup, 0) / results.length,
        accuracyLoss: results.reduce((sum, r) => sum + r.metrics.accuracyLoss, 0) / results.length,
        optimizationTime: results.reduce((sum, r) => sum + r.optimizationTime, 0) / results.length
      };

      // 找出最佳结果
      const bestSizeReduction = results.reduce((best, current) => 
        current.metrics.sizeReduction > best.metrics.sizeReduction ? current : best
      );
      
      const bestSpeedup = results.reduce((best, current) => 
        current.metrics.speedup > best.metrics.speedup ? current : best
      );
      
      const bestAccuracyPreservation = results.reduce((best, current) => 
        current.metrics.accuracyLoss < best.metrics.accuracyLoss ? current : best
      );

      const stats = {
        totalOptimizations: results.length,
        strategiesUsage,
        averageMetrics,
        bestResults: {
          bestSizeReduction,
          bestSpeedup,
          bestAccuracyPreservation
        }
      };

      this.logger.log('获取优化统计信息');
      return stats;

    } catch (error) {
      this.logger.error(`获取优化统计信息失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 生成优化建议
   */
  private generateOptimizationRecommendations(request: any): any[] {
    const recommendations = [];
    
    // 基于目标指标生成建议
    if (request.targetMetrics.targetModelSize) {
      // 推荐量化和剪枝
      recommendations.push({
        strategy: OptimizationStrategy.QUANTIZATION,
        config: {
          strategy: OptimizationStrategy.QUANTIZATION,
          parameters: { quantizationBits: 8 },
          targetMetrics: request.targetMetrics
        },
        expectedResults: {
          sizeReduction: 75,
          speedup: 2.0,
          accuracyLoss: 0.02
        },
        priority: 1
      });
    }
    
    if (request.targetMetrics.targetInferenceTime) {
      // 推荐剪枝和混合精度
      recommendations.push({
        strategy: OptimizationStrategy.PRUNING,
        config: {
          strategy: OptimizationStrategy.PRUNING,
          parameters: { pruningRatio: 0.5 },
          targetMetrics: request.targetMetrics
        },
        expectedResults: {
          sizeReduction: 50,
          speedup: 1.8,
          accuracyLoss: 0.03
        },
        priority: 2
      });
    }
    
    return recommendations.sort((a, b) => a.priority - b.priority);
  }
}
