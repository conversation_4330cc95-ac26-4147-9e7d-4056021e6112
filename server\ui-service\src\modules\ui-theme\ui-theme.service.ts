import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { Model, Types } from 'mongoose';
import Redis from 'ioredis';
import { UITheme, UIThemeDocument, ThemeType, ThemeStatus } from './schemas/ui-theme.schema';
import { CreateUIThemeDto, UpdateUIThemeDto, QueryUIThemeDto } from './dto/ui-theme.dto';

@Injectable()
export class UIThemeService {
  constructor(
    @InjectModel(UITheme.name) private themeModel: Model<UIThemeDocument>,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  /**
   * 创建UI主题
   */
  async create(createThemeDto: CreateUIThemeDto, userId: Types.ObjectId): Promise<UITheme> {
    try {
      // 检查主题名称是否已存在
      const existingTheme = await this.themeModel.findOne({
        name: createThemeDto.name,
        organizationId: createThemeDto.organizationId,
      });

      if (existingTheme) {
        throw new BadRequestException('主题名称已存在');
      }

      // 创建主题
      const theme = new this.themeModel({
        ...createThemeDto,
        createdBy: userId,
        updatedBy: userId,
      });

      const savedTheme = await theme.save();

      // 清除相关缓存
      await this.clearThemeCache(createThemeDto.type);

      return savedTheme;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('创建主题失败');
    }
  }

  /**
   * 查询UI主题列表
   */
  async findAll(queryDto: QueryUIThemeDto, userId: Types.ObjectId): Promise<{
    themes: UITheme[];
    total: number;
    page: number;
    limit: number;
  }> {
    const { page = 1, limit = 20, search, sortBy = 'createdAt', sortOrder = 'desc', ...filters } = queryDto;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const query: any = {
      isActive: true,
    };

    // 添加过滤条件
    if (filters.type) query.type = filters.type;
    if (filters.status) query.status = filters.status;
    if (filters.organizationId) query.organizationId = filters.organizationId;
    if (filters.tags) query.tags = { $in: filters.tags.split(',') };
    if (typeof filters.isPublic === 'boolean') query.isPublic = filters.isPublic;

    // 添加搜索条件
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $regex: search, $options: 'i' } },
      ];
    }

    // 添加权限过滤
    query.$or = [
      { isPublic: true },
      { createdBy: userId },
      // 这里可以添加组织权限检查
    ];

    // 排序
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // 执行查询
    const [themes, total] = await Promise.all([
      this.themeModel
        .find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('createdBy', 'username email')
        .populate('updatedBy', 'username email')
        .exec(),
      this.themeModel.countDocuments(query),
    ]);

    return {
      themes,
      total,
      page,
      limit,
    };
  }

  /**
   * 获取单个UI主题
   */
  async findOne(id: string, userId: Types.ObjectId): Promise<UITheme> {
    // 尝试从缓存获取
    const cacheKey = `ui_theme:${id}`;
    const cached = await this.redis.get(cacheKey);
    if (cached) {
      const theme = JSON.parse(cached);
      if (this.canAccessTheme(theme, userId)) {
        return theme;
      }
    }

    // 从数据库获取
    const theme = await this.themeModel
      .findById(id)
      .populate('createdBy', 'username email')
      .populate('updatedBy', 'username email')
      .exec();

    if (!theme) {
      throw new NotFoundException('主题不存在');
    }

    // 检查访问权限
    if (!this.canAccessTheme(theme, userId)) {
      throw new ForbiddenException('没有访问权限');
    }

    // 增加浏览次数
    await this.incrementViews(theme);

    // 缓存主题
    await this.redis.setex(cacheKey, 1800, JSON.stringify(theme));

    return theme;
  }

  /**
   * 更新UI主题
   */
  async update(id: string, updateThemeDto: UpdateUIThemeDto, userId: Types.ObjectId): Promise<UITheme> {
    const theme = await this.themeModel.findById(id);

    if (!theme) {
      throw new NotFoundException('主题不存在');
    }

    // 检查编辑权限
    if (!this.canEditTheme(theme, userId)) {
      throw new ForbiddenException('没有编辑权限');
    }

    // 更新主题
    Object.assign(theme, updateThemeDto);
    theme.updatedBy = userId;
    theme.updatedAt = new Date();

    const updatedTheme = await theme.save();

    // 清除缓存
    await this.redis.del(`ui_theme:${id}`);
    await this.clearThemeCache(theme.type);

    return updatedTheme;
  }

  /**
   * 删除UI主题
   */
  async remove(id: string, userId: Types.ObjectId): Promise<void> {
    const theme = await this.themeModel.findById(id);

    if (!theme) {
      throw new NotFoundException('主题不存在');
    }

    // 检查删除权限
    if (!this.canEditTheme(theme, userId)) {
      throw new ForbiddenException('没有删除权限');
    }

    // 软删除
    await this.softDeleteTheme(theme, userId);

    // 清除缓存
    await this.redis.del(`ui_theme:${id}`);
    await this.clearThemeCache(theme.type);
  }

  /**
   * 发布主题
   */
  async publish(id: string, userId: Types.ObjectId): Promise<UITheme> {
    const theme = await this.themeModel.findById(id);

    if (!theme) {
      throw new NotFoundException('主题不存在');
    }

    if (!this.canEditTheme(theme, userId)) {
      throw new ForbiddenException('没有发布权限');
    }

    if (theme.status === ThemeStatus.PUBLISHED) {
      throw new BadRequestException('主题已经发布');
    }

    theme.status = ThemeStatus.PUBLISHED;
    theme.updatedBy = userId;
    theme.updatedAt = new Date();

    const publishedTheme = await theme.save();

    // 清除缓存
    await this.redis.del(`ui_theme:${id}`);

    return publishedTheme;
  }

  /**
   * 下载主题
   */
  async download(id: string, userId: Types.ObjectId): Promise<UITheme> {
    const theme = await this.findOne(id, userId);
    
    // 增加下载次数
    await this.incrementDownloads(theme);

    return theme;
  }

  /**
   * 检查主题访问权限
   */
  private canAccessTheme(theme: UITheme, userId: Types.ObjectId): boolean {
    if (theme.isPublic) {
      return true;
    }

    return theme.createdBy.equals(userId);
  }

  /**
   * 检查主题编辑权限
   */
  private canEditTheme(theme: UITheme, userId: Types.ObjectId): boolean {
    return theme.createdBy.equals(userId);
  }

  /**
   * 增加浏览次数
   */
  private async incrementViews(theme: any): Promise<void> {
    theme.statistics = theme.statistics || {};
    theme.statistics.views = (theme.statistics.views || 0) + 1;
    await theme.save();
  }

  /**
   * 增加下载次数
   */
  private async incrementDownloads(theme: any): Promise<void> {
    theme.statistics = theme.statistics || {};
    theme.statistics.downloads = (theme.statistics.downloads || 0) + 1;
    await theme.save();
  }

  /**
   * 软删除主题
   */
  private async softDeleteTheme(theme: any, userId: Types.ObjectId): Promise<void> {
    theme.deletedAt = new Date();
    theme.deletedBy = userId;
    theme.isActive = false;
    await theme.save();
  }

  /**
   * 清除主题缓存
   */
  private async clearThemeCache(type: ThemeType): Promise<void> {
    const pattern = `ui_theme*:${type}:*`;
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }
}
