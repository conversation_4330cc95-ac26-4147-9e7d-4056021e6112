{"displayName": "UI Service E2E", "moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "testRegex": ".e2e-spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["src/**/*.(t|j)s", "!src/**/*.spec.ts", "!src/**/*.interface.ts", "!src/**/*.dto.ts", "!src/**/*.schema.ts", "!src/main.ts"], "coverageDirectory": "coverage-e2e", "setupFilesAfterEnv": ["<rootDir>/setup-e2e.ts"], "testTimeout": 30000, "maxWorkers": 1, "forceExit": true, "detectOpenHandles": true}