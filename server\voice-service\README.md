# DL引擎语音服务 (Voice Service)

DL引擎的语音服务模块，提供语音识别、语音合成、音频处理和嘴形同步功能。

## 🚀 功能特性

### 核心功能
- **语音识别 (ASR)**: 支持多种语音识别提供商
- **语音合成 (TTS)**: 支持多种语音合成提供商
- **音频处理**: 音频格式转换、降噪、标准化等
- **嘴形同步**: 基于音素和音频的嘴形动画生成

### 支持的服务提供商
- **Azure Cognitive Services**: 语音识别和合成
- **OpenAI Whisper**: 语音识别
- **Google Cloud Speech**: 语音识别和合成
- **百度AI**: 语音识别和合成
- **腾讯云**: 语音识别和合成

### 技术特性
- 🔄 实时语音识别
- 🎵 多格式音频处理
- 🗣️ 多语言支持
- 👄 嘴形同步生成
- 🔌 WebSocket实时通信
- 📊 服务监控和统计
- 🐳 Docker容器化部署

## 📋 系统要求

- Node.js 18+
- Redis 6+
- FFmpeg (音频处理)
- 至少2GB内存
- 至少10GB磁盘空间

## 🛠️ 安装和配置

### 1. 克隆项目
```bash
git clone <repository-url>
cd server/voice-service
```

### 2. 安装依赖
```bash
npm install
```

### 3. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置必要的环境变量
```

### 4. 启动服务

#### 开发模式
```bash
npm run start:dev
```

#### 生产模式
```bash
npm run build
npm run start:prod
```

#### Docker部署
```bash
docker-compose up -d
```

## 🔧 配置说明

### 环境变量配置

#### 基础配置
```env
NODE_ENV=development
VOICE_SERVICE_HOST=localhost
VOICE_SERVICE_PORT=3010
VOICE_HTTP_PORT=4010
```

#### Azure语音服务
```env
AZURE_SPEECH_KEY=your-azure-speech-key
AZURE_SPEECH_REGION=your-azure-region
```

#### OpenAI配置
```env
OPENAI_API_KEY=your-openai-api-key
```

#### Redis配置
```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=1
```

## 📚 API文档

### REST API
服务启动后，访问 `http://localhost:4010/api/docs` 查看Swagger API文档。

### WebSocket API
连接地址: `ws://localhost:4010/voice`

#### 主要事件
- `configure`: 配置语音服务
- `start-recognition`: 开始语音识别
- `stop-recognition`: 停止语音识别
- `synthesize`: 语音合成
- `audio-data`: 发送音频数据
- `generate-lip-sync`: 生成嘴形同步

### 主要API端点

#### 语音识别
```http
POST /api/v1/voice/recognize
Content-Type: multipart/form-data

{
  "audio": "音频文件",
  "provider": "azure",
  "language": "zh-CN"
}
```

#### 语音合成
```http
POST /api/v1/voice/synthesize
Content-Type: application/json

{
  "text": "要合成的文本",
  "provider": "azure",
  "voice": "zh-CN-XiaoxiaoNeural",
  "language": "zh-CN"
}
```

#### 音频处理
```http
POST /api/v1/voice/process-audio
Content-Type: multipart/form-data

{
  "audio": "音频文件",
  "outputFormat": "wav",
  "normalize": true
}
```

#### 嘴形同步
```http
POST /api/v1/voice/generate-lip-sync
Content-Type: multipart/form-data

{
  "text": "文本内容",
  "audio": "音频文件（可选）",
  "method": "hybrid",
  "language": "zh-CN"
}
```

## 🏗️ 项目结构

```
src/
├── audio-processing/     # 音频处理模块
├── common/              # 公共组件
│   ├── dto/            # 数据传输对象
│   ├── filters/        # 异常过滤器
│   ├── guards/         # 认证守卫
│   └── interceptors/   # 拦截器
├── health/             # 健康检查模块
├── lip-sync/           # 嘴形同步模块
├── speech-recognition/ # 语音识别模块
├── speech-synthesis/   # 语音合成模块
├── voice/              # 语音服务主模块
├── app.module.ts       # 应用主模块
└── main.ts            # 应用入口
```

## 🧪 测试

### 运行测试
```bash
# 单元测试
npm run test

# 测试覆盖率
npm run test:cov

# 端到端测试
npm run test:e2e
```

## 📊 监控和运维

### 健康检查
```bash
curl http://localhost:4010/health
```

### 服务统计
```bash
curl http://localhost:4010/api/v1/voice/statistics
```

### 日志查看
```bash
# Docker环境
docker-compose logs -f voice-service

# 本地环境
tail -f logs/voice-service.log
```

## 🔍 故障排除

### 常见问题

1. **FFmpeg未找到**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install ffmpeg
   
   # macOS
   brew install ffmpeg
   
   # Windows
   # 下载FFmpeg并添加到PATH
   ```

2. **Azure语音服务连接失败**
   - 检查API密钥和区域配置
   - 确认网络连接正常
   - 验证配额限制

3. **Redis连接失败**
   - 检查Redis服务状态
   - 验证连接配置
   - 查看防火墙设置

4. **内存不足**
   - 增加系统内存
   - 调整Node.js内存限制
   - 优化音频处理参数

## 🤝 开发指南

### 添加新的语音提供商

1. 在相应的服务类中添加新的提供商枚举
2. 实现提供商特定的方法
3. 更新配置和文档
4. 添加测试用例

### 代码规范
- 使用ESLint和Prettier进行代码格式化
- 遵循NestJS最佳实践
- 编写单元测试和集成测试
- 添加适当的错误处理和日志记录

## 📄 许可证

MIT License

## 🆘 支持

如有问题或建议，请提交Issue或联系开发团队。
