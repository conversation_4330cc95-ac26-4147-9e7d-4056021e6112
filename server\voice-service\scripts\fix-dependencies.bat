@echo off
echo Fixing Voice Service dependencies...
echo.

REM Check Node.js version
echo Checking Node.js version...
node --version
if %errorlevel% neq 0 (
    echo Node.js not installed or not in PATH
    pause
    exit /b 1
)

REM Check npm version
echo Checking npm version...
npm --version
if %errorlevel% neq 0 (
    echo npm not installed or not in PATH
    pause
    exit /b 1
)

REM Clean existing node_modules and package-lock.json
echo Cleaning existing dependencies...
if exist node_modules (
    rmdir /s /q node_modules
    echo Deleted node_modules directory
)

if exist package-lock.json (
    del package-lock.json
    echo Deleted package-lock.json file
)

REM Clean npm cache
echo Cleaning npm cache...
npm cache clean --force
if %errorlevel% equ 0 (
    echo npm cache cleaned successfully
) else (
    echo npm cache clean failed, continuing...
)

REM Install dependencies
echo Installing project dependencies...
npm install --legacy-peer-deps

if %errorlevel% equ 0 (
    echo Dependencies installed successfully!

    REM Verify key dependencies
    echo Verifying key dependencies...

    npm list @nestjs/core >nul 2>&1
    if %errorlevel% equ 0 (
        echo @nestjs/core installed successfully
    ) else (
        echo @nestjs/core installation failed
    )

    npm list @nestjs/event-emitter >nul 2>&1
    if %errorlevel% equ 0 (
        echo @nestjs/event-emitter installed successfully
    ) else (
        echo @nestjs/event-emitter installation failed
    )

    npm list microsoft-cognitiveservices-speech-sdk >nul 2>&1
    if %errorlevel% equ 0 (
        echo microsoft-cognitiveservices-speech-sdk installed successfully
    ) else (
        echo microsoft-cognitiveservices-speech-sdk installation failed
    )

    npm list fluent-ffmpeg >nul 2>&1
    if %errorlevel% equ 0 (
        echo fluent-ffmpeg installed successfully
    ) else (
        echo fluent-ffmpeg installation failed
    )

    echo.
    echo Dependencies fix completed!
    echo.
    echo Next steps:
    echo 1. Configure environment: copy .env.example .env
    echo 2. Edit .env file to configure voice service API keys
    echo 3. Start Redis: docker-compose up -d redis
    echo 4. Start service: npm run start:dev
    echo.

) else (
    echo Dependencies installation failed!
    echo.
    echo Possible solutions:
    echo 1. Check network connection
    echo 2. Try different npm registry
    echo 3. Check Node.js version compatibility (recommended 18+)
    echo 4. Manually install problematic packages
    echo.
    echo Manual installation commands:
    echo npm install @nestjs/event-emitter@^2.0.0 --legacy-peer-deps
    echo npm install microsoft-cognitiveservices-speech-sdk --legacy-peer-deps
    echo npm install fluent-ffmpeg --legacy-peer-deps
    echo.
)

pause
