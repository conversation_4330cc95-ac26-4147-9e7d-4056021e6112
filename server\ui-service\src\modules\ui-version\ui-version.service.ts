import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { UIVersion, UIVersionDocument, VersionType } from './schemas/ui-version.schema';

@Injectable()
export class UIVersionService {
  constructor(
    @InjectModel(UIVersion.name) private versionModel: Model<UIVersionDocument>,
  ) {}

  /**
   * 创建版本
   */
  async createVersion(
    resourceId: Types.ObjectId,
    resourceType: string,
    version: string,
    versionType: VersionType,
    data: any,
    description?: string,
    changes?: string[],
    userId?: Types.ObjectId,
  ): Promise<UIVersion> {
    const versionDoc = new this.versionModel({
      resourceId,
      resourceType,
      version,
      versionType,
      data,
      description,
      changes: changes || [],
      createdBy: userId,
    });

    return versionDoc.save();
  }

  /**
   * 获取资源的版本历史
   */
  async getVersionHistory(resourceId: Types.ObjectId, limit = 50): Promise<UIVersion[]> {
    return this.versionModel
      .find({ resourceId })
      .sort({ createdAt: -1 })
      .limit(limit)
      .populate('createdBy', 'username email')
      .exec();
  }

  /**
   * 获取指定版本
   */
  async getVersion(resourceId: Types.ObjectId, version: string): Promise<UIVersion | null> {
    return this.versionModel
      .findOne({ resourceId, version })
      .populate('createdBy', 'username email')
      .exec();
  }

  /**
   * 获取最新版本
   */
  async getLatestVersion(resourceId: Types.ObjectId): Promise<UIVersion | null> {
    return this.versionModel
      .findOne({ resourceId })
      .sort({ createdAt: -1 })
      .populate('createdBy', 'username email')
      .exec();
  }
}
