import { Injectable, Logger } from '@nestjs/common';
import { Socket } from 'socket.io';

interface ClientInfo {
  socket: Socket;
  userId?: string;
  rooms: Set<string>;
  connectedAt: Date;
}

@Injectable()
export class WebSocketService {
  private readonly logger = new Logger(WebSocketService.name);
  private clients = new Map<string, ClientInfo>();
  private rooms = new Map<string, Set<string>>();

  /**
   * 添加客户端
   */
  addClient(socket: Socket, userId?: string) {
    const clientInfo: ClientInfo = {
      socket,
      userId,
      rooms: new Set(),
      connectedAt: new Date(),
    };

    this.clients.set(socket.id, clientInfo);
    this.logger.log(`添加客户端: ${socket.id}, 用户: ${userId || '匿名'}`);
  }

  /**
   * 移除客户端
   */
  removeClient(clientId: string) {
    const client = this.clients.get(clientId);
    if (client) {
      // 从所有房间中移除
      client.rooms.forEach(room => {
        this.leaveRoom(clientId, room);
      });
      
      this.clients.delete(clientId);
      this.logger.log(`移除客户端: ${clientId}`);
    }
  }

  /**
   * 加入房间
   */
  joinRoom(clientId: string, room: string, userId?: string) {
    const client = this.clients.get(clientId);
    if (client) {
      client.rooms.add(room);
      if (userId) {
        client.userId = userId;
      }

      // 更新房间成员列表
      if (!this.rooms.has(room)) {
        this.rooms.set(room, new Set());
      }
      this.rooms.get(room)!.add(clientId);

      this.logger.log(`客户端 ${clientId} 加入房间: ${room}`);
    }
  }

  /**
   * 离开房间
   */
  leaveRoom(clientId: string, room: string) {
    const client = this.clients.get(clientId);
    if (client) {
      client.rooms.delete(room);

      // 更新房间成员列表
      const roomClients = this.rooms.get(room);
      if (roomClients) {
        roomClients.delete(clientId);
        if (roomClients.size === 0) {
          this.rooms.delete(room);
        }
      }

      this.logger.log(`客户端 ${clientId} 离开房间: ${room}`);
    }
  }

  /**
   * 获取房间内的客户端列表
   */
  getRoomClients(room: string): string[] {
    return Array.from(this.rooms.get(room) || []);
  }

  /**
   * 获取客户端信息
   */
  getClientInfo(clientId: string): ClientInfo | undefined {
    return this.clients.get(clientId);
  }

  /**
   * 获取用户的所有客户端
   */
  getUserClients(userId: string): ClientInfo[] {
    return Array.from(this.clients.values()).filter(
      client => client.userId === userId
    );
  }

  /**
   * 发送消息到指定客户端
   */
  sendToClient(clientId: string, event: string, data: any) {
    const client = this.clients.get(clientId);
    if (client) {
      client.socket.emit(event, data);
    }
  }

  /**
   * 发送消息到指定用户的所有客户端
   */
  sendToUser(userId: string, event: string, data: any) {
    const userClients = this.getUserClients(userId);
    userClients.forEach(client => {
      client.socket.emit(event, data);
    });
  }

  /**
   * 发送消息到房间
   */
  sendToRoom(room: string, event: string, data: any) {
    const roomClients = this.getRoomClients(room);
    roomClients.forEach(clientId => {
      this.sendToClient(clientId, event, data);
    });
  }

  /**
   * 广播消息到所有客户端
   */
  broadcast(event: string, data: any) {
    this.clients.forEach(client => {
      client.socket.emit(event, data);
    });
  }

  /**
   * 获取连接统计信息
   */
  getStats() {
    return {
      totalClients: this.clients.size,
      totalRooms: this.rooms.size,
      clientsPerRoom: Array.from(this.rooms.entries()).map(([room, clients]) => ({
        room,
        clientCount: clients.size,
      })),
      connectedUsers: Array.from(new Set(
        Array.from(this.clients.values())
          .map(client => client.userId)
          .filter(userId => userId)
      )).length,
    };
  }

  /**
   * 清理空房间
   */
  cleanupEmptyRooms() {
    const emptyRooms = Array.from(this.rooms.entries())
      .filter(([, clients]) => clients.size === 0)
      .map(([room]) => room);

    emptyRooms.forEach(room => {
      this.rooms.delete(room);
      this.logger.log(`清理空房间: ${room}`);
    });

    return emptyRooms.length;
  }
}
