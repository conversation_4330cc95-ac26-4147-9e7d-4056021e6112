import { IsString, IsOptional, IsObject, IsEnum, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsBoolean } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';

export enum ExecutionStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  STOPPED = 'stopped',
}

export class ExecuteScriptDto {
  @ApiPropertyOptional({ description: '执行输入参数', type: 'object' })
  @IsOptional()
  @IsObject()
  input?: Record<string, any>;

  @ApiPropertyOptional({ description: '执行配置', type: 'object' })
  @IsOptional()
  @IsObject()
  config?: {
    timeout?: number;
    memoryLimit?: number;
    enableDebug?: boolean;
    enableProfiling?: boolean;
    [key: string]: any;
  };

  @ApiPropertyOptional({ description: '执行环境' })
  @IsOptional()
  @IsString()
  environment?: string;

  @ApiPropertyOptional({ description: '是否异步执行', default: false })
  @IsOptional()
  @IsBoolean()
  async?: boolean;
}

export class UpdateExecutionDto {
  @ApiPropertyOptional({ description: '执行状态', enum: ExecutionStatus })
  @IsOptional()
  @IsEnum(ExecutionStatus)
  status?: ExecutionStatus;

  @ApiPropertyOptional({ description: '执行输出', type: 'object' })
  @IsOptional()
  @IsObject()
  output?: Record<string, any>;

  @ApiPropertyOptional({ description: '错误信息' })
  @IsOptional()
  @IsString()
  error?: string;

  @ApiPropertyOptional({ description: '执行日志', type: [String] })
  @IsOptional()
  logs?: string[];

  @ApiPropertyOptional({ description: '性能指标', type: 'object' })
  @IsOptional()
  @IsObject()
  metrics?: {
    memoryUsage?: number;
    cpuUsage?: number;
    executionTime?: number;
    [key: string]: any;
  };
}

export class QueryExecutionDto {
  @ApiPropertyOptional({ description: '页码', minimum: 1, default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100, default: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({ description: '执行状态', enum: ExecutionStatus })
  @IsOptional()
  @IsEnum(ExecutionStatus)
  status?: ExecutionStatus;

  @ApiPropertyOptional({ description: '脚本ID' })
  @IsOptional()
  @IsString()
  scriptId?: string;

  @ApiPropertyOptional({ description: '排序字段', default: 'startTime' })
  @IsOptional()
  @IsString()
  sortBy?: string = 'startTime';

  @ApiPropertyOptional({ description: '排序方向', enum: ['ASC', 'DESC'], default: 'DESC' })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';

  @ApiPropertyOptional({ description: '开始时间' })
  @IsOptional()
  @IsString()
  startTime?: string;

  @ApiPropertyOptional({ description: '结束时间' })
  @IsOptional()
  @IsString()
  endTime?: string;
}

export class ExecutionStatsDto {
  @ApiProperty({ description: '总执行次数' })
  total: number;

  @ApiProperty({ description: '成功次数' })
  completed: number;

  @ApiProperty({ description: '失败次数' })
  failed: number;

  @ApiProperty({ description: '运行中次数' })
  running: number;

  @ApiProperty({ description: '平均执行时间(ms)' })
  avgDuration: number;

  @ApiPropertyOptional({ description: '成功率' })
  successRate?: number;

  @ApiPropertyOptional({ description: '最近执行时间' })
  lastExecutionTime?: Date;
}

export class ExecutionLogDto {
  @ApiProperty({ description: '执行ID' })
  executionId: string;

  @ApiProperty({ description: '日志列表', type: [String] })
  logs: string[];

  @ApiPropertyOptional({ description: '执行输出', type: 'object' })
  output?: Record<string, any>;

  @ApiPropertyOptional({ description: '错误信息' })
  error?: string;
}
