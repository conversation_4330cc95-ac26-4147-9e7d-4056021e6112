import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';

import compression from 'compression';
import helmet from 'helmet';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // 配置微服务
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.TCP,
    options: {
      host: configService.get<string>('VOICE_SERVICE_HOST', 'localhost'),
      port: configService.get<number>('VOICE_SERVICE_PORT', 3010),
    },
  });

  // WebSocket支持已通过@nestjs/platform-socket.io自动配置

  // 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );

  // 启用CORS
  app.enableCors({
    origin: configService.get<string>('CORS_ORIGIN', '*'),
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });

  // 启用压缩
  app.use(compression());

  // 启用安全头
  app.use(helmet());

  // API前缀
  app.setGlobalPrefix('api/v1');

  // Swagger文档配置
  const config = new DocumentBuilder()
    .setTitle('DL引擎语音服务API')
    .setDescription('提供语音识别、语音合成、音频处理和嘴形同步功能')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('speech-recognition', '语音识别')
    .addTag('speech-synthesis', '语音合成')
    .addTag('audio-processing', '音频处理')
    .addTag('lip-sync', '嘴形同步')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });

  // 健康检查端点
  app.getHttpAdapter().get('/health', (_req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'voice-service',
      version: '1.0.0',
    });
  });

  // 启动微服务
  await app.startAllMicroservices();

  // 启动HTTP服务
  const httpPort = configService.get<number>('VOICE_HTTP_PORT', 4010);
  await app.listen(httpPort);

  console.log(`语音服务已启动，微服务端口: ${configService.get<number>('VOICE_SERVICE_PORT', 3010)}, HTTP端口: ${httpPort}`);
}

bootstrap();
