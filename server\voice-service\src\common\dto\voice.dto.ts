import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsNumber,
  IsBoolean,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>rray,
} from 'class-validator';
import { SpeechProvider } from '../../speech-recognition/speech-recognition.service';
import { TTSProvider } from '../../speech-synthesis/speech-synthesis.service';
import { AudioFormat } from '../../audio-processing/audio-processing.service';

/**
 * 语音识别请求DTO
 */
export class SpeechRecognitionRequestDto {
  @ApiProperty({
    description: '语音识别提供商',
    enum: SpeechProvider,
    default: SpeechProvider.AZURE,
  })
  @IsEnum(SpeechProvider)
  @IsOptional()
  provider?: SpeechProvider = SpeechProvider.AZURE;

  @ApiProperty({
    description: '语言代码',
    example: 'zh-CN',
    default: 'zh-CN',
  })
  @IsString()
  @IsOptional()
  language?: string = 'zh-CN';

  @ApiProperty({
    description: '采样率',
    example: 16000,
  })
  @IsNumber()
  @IsOptional()
  sampleRate?: number;

  @ApiProperty({
    description: '声道数',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  channels?: number;

  @ApiProperty({
    description: '是否启用标点符号',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  enablePunctuation?: boolean = true;

  @ApiProperty({
    description: '是否启用词时间戳',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  enableWordTimestamps?: boolean = false;

  @ApiProperty({
    description: '是否启用说话人分离',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  enableSpeakerDiarization?: boolean = false;

  @ApiProperty({
    description: '最大说话人数',
    example: 2,
  })
  @IsNumber()
  @IsOptional()
  @Min(1)
  @Max(10)
  maxSpeakers?: number;
}

/**
 * 语音合成请求DTO
 */
export class SpeechSynthesisRequestDto {
  @ApiProperty({
    description: '要合成的文本',
    example: '您好，欢迎使用语音合成服务。',
  })
  @IsString()
  text: string;

  @ApiProperty({
    description: '语音合成提供商',
    enum: TTSProvider,
    default: TTSProvider.AZURE,
  })
  @IsEnum(TTSProvider)
  @IsOptional()
  provider?: TTSProvider = TTSProvider.AZURE;

  @ApiProperty({
    description: '语音名称',
    example: 'zh-CN-XiaoxiaoNeural',
  })
  @IsString()
  @IsOptional()
  voice?: string = 'zh-CN-XiaoxiaoNeural';

  @ApiProperty({
    description: '语言代码',
    example: 'zh-CN',
  })
  @IsString()
  @IsOptional()
  language?: string = 'zh-CN';

  @ApiProperty({
    description: '语速 (0.5-2.0)',
    example: 1.0,
    minimum: 0.5,
    maximum: 2.0,
  })
  @IsNumber()
  @IsOptional()
  @Min(0.5)
  @Max(2.0)
  rate?: number = 1.0;

  @ApiProperty({
    description: '音调 (0.5-2.0)',
    example: 1.0,
    minimum: 0.5,
    maximum: 2.0,
  })
  @IsNumber()
  @IsOptional()
  @Min(0.5)
  @Max(2.0)
  pitch?: number = 1.0;

  @ApiProperty({
    description: '音量 (0.0-1.0)',
    example: 1.0,
    minimum: 0.0,
    maximum: 1.0,
  })
  @IsNumber()
  @IsOptional()
  @Min(0.0)
  @Max(1.0)
  volume?: number = 1.0;

  @ApiProperty({
    description: '语音风格',
    example: 'friendly',
  })
  @IsString()
  @IsOptional()
  style?: string;

  @ApiProperty({
    description: '情感',
    example: 'happy',
  })
  @IsString()
  @IsOptional()
  emotion?: string;

  @ApiProperty({
    description: '输出格式',
    enum: ['wav', 'mp3', 'ogg'],
    default: 'wav',
  })
  @IsString()
  @IsOptional()
  outputFormat?: 'wav' | 'mp3' | 'ogg' = 'wav';

  @ApiProperty({
    description: '采样率',
    example: 16000,
  })
  @IsNumber()
  @IsOptional()
  sampleRate?: number;
}

/**
 * 音频处理请求DTO
 */
export class AudioProcessingRequestDto {
  @ApiProperty({
    description: '输出格式',
    enum: AudioFormat,
    default: AudioFormat.WAV,
  })
  @IsEnum(AudioFormat)
  outputFormat: AudioFormat = AudioFormat.WAV;

  @ApiProperty({
    description: '采样率',
    example: 16000,
  })
  @IsNumber()
  @IsOptional()
  sampleRate?: number;

  @ApiProperty({
    description: '声道数',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  channels?: number;

  @ApiProperty({
    description: '比特率',
    example: 128000,
  })
  @IsNumber()
  @IsOptional()
  bitRate?: number;

  @ApiProperty({
    description: '音量调整',
    example: 1.0,
  })
  @IsNumber()
  @IsOptional()
  volume?: number;

  @ApiProperty({
    description: '是否标准化',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  normalize?: boolean = false;

  @ApiProperty({
    description: '是否降噪',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  removeNoise?: boolean = false;

  @ApiProperty({
    description: '是否去除静音',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  trimSilence?: boolean = false;

  @ApiProperty({
    description: '淡入时间（秒）',
    example: 0.5,
  })
  @IsNumber()
  @IsOptional()
  fadeIn?: number;

  @ApiProperty({
    description: '淡出时间（秒）',
    example: 0.5,
  })
  @IsNumber()
  @IsOptional()
  fadeOut?: number;
}

/**
 * 嘴形同步请求DTO
 */
export class LipSyncRequestDto {
  @ApiProperty({
    description: '文本内容',
    example: '您好，欢迎使用嘴形同步功能。',
  })
  @IsString()
  text: string;

  @ApiProperty({
    description: '生成方法',
    enum: ['phoneme', 'audio', 'hybrid'],
    default: 'phoneme',
  })
  @IsString()
  @IsOptional()
  method?: 'phoneme' | 'audio' | 'hybrid' = 'phoneme';

  @ApiProperty({
    description: '语言代码',
    example: 'zh-CN',
  })
  @IsString()
  @IsOptional()
  language?: string = 'zh-CN';

  @ApiProperty({
    description: '帧率',
    example: 30,
    default: 30,
  })
  @IsNumber()
  @IsOptional()
  @Min(15)
  @Max(60)
  frameRate?: number = 30;

  @ApiProperty({
    description: '平滑度 (0.0-1.0)',
    example: 0.5,
    minimum: 0.0,
    maximum: 1.0,
  })
  @IsNumber()
  @IsOptional()
  @Min(0.0)
  @Max(1.0)
  smoothing?: number = 0.5;

  @ApiProperty({
    description: '强度 (0.0-1.0)',
    example: 1.0,
    minimum: 0.0,
    maximum: 1.0,
  })
  @IsNumber()
  @IsOptional()
  @Min(0.0)
  @Max(1.0)
  intensity?: number = 1.0;

  @ApiProperty({
    description: '是否启用共振峰分析',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  enableFormantAnalysis?: boolean = false;

  @ApiProperty({
    description: '是否启用基频分析',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  enablePitchAnalysis?: boolean = false;
}
