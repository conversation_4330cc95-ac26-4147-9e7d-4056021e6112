import { Injectable } from '@nestjs/common';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { HealthIndicatorResult, HealthIndicator } from '@nestjs/terminus';
import Redis from 'ioredis';

@Injectable()
export class HealthService extends HealthIndicator {
  constructor(
    @InjectRedis() private readonly redis: Redis,
  ) {
    super();
  }

  /**
   * Redis连接健康检查
   */
  async checkRedis(): Promise<HealthIndicatorResult> {
    try {
      await this.redis.ping();
      return this.getStatus('redis', true);
    } catch (error) {
      return this.getStatus('redis', false, { message: error.message });
    }
  }

  /**
   * UI服务业务健康检查
   */
  async checkUIService(): Promise<HealthIndicatorResult> {
    try {
      // 检查关键业务功能
      const checks = await Promise.all([
        this.checkTemplateService(),
        this.checkConfigService(),
        this.checkThemeService(),
      ]);

      const allHealthy = checks.every(check => check.healthy);
      
      return this.getStatus('ui-service', allHealthy, {
        template: checks[0].healthy,
        config: checks[1].healthy,
        theme: checks[2].healthy,
      });
    } catch (error) {
      return this.getStatus('ui-service', false, { message: error.message });
    }
  }

  /**
   * 检查模板服务
   */
  private async checkTemplateService(): Promise<{ healthy: boolean }> {
    try {
      // 这里可以添加具体的模板服务检查逻辑
      // 例如：检查数据库连接、缓存状态等
      return { healthy: true };
    } catch (error) {
      return { healthy: false };
    }
  }

  /**
   * 检查配置服务
   */
  private async checkConfigService(): Promise<{ healthy: boolean }> {
    try {
      // 这里可以添加具体的配置服务检查逻辑
      return { healthy: true };
    } catch (error) {
      return { healthy: false };
    }
  }

  /**
   * 检查主题服务
   */
  private async checkThemeService(): Promise<{ healthy: boolean }> {
    try {
      // 这里可以添加具体的主题服务检查逻辑
      return { healthy: true };
    } catch (error) {
      return { healthy: false };
    }
  }
}
