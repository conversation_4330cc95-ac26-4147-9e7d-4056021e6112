@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM DL引擎微服务项目完善实施脚本 (Windows版本)
REM 用于自动化执行项目完善计划

echo ========================================
echo DL引擎微服务项目完善实施脚本
echo ========================================
echo.

REM 检查是否在正确的目录
if not exist "server" (
    echo [ERROR] 请在项目根目录运行此脚本
    pause
    exit /b 1
)

REM 定义项目数组
set "phase1_projects=ai-service ai-engine-service cloud-edge-orchestration-service project-service asset-service collaboration-service performance-service user-service"
set "phase2_projects=game-server ui-service behavior-decision-service edge-enhancement ecosystem-service spatial-service human-machine-collaboration-service monitoring-service analytics-service enterprise-integration-service"
set "phase3_projects=blockchain-service 5g-network-service"

:main_menu
echo 请选择要执行的操作:
echo 1. 执行第一阶段完善 (高优先级项目)
echo 2. 执行第二阶段完善 (中优先级项目)
echo 3. 执行第三阶段完善 (低优先级项目)
echo 4. 执行全部阶段完善
echo 5. 生成项目状态报告
echo 6. 检查所有项目状态
echo 0. 退出
echo.
set /p choice="请输入选择 (0-6): "

if "%choice%"=="1" goto phase1
if "%choice%"=="2" goto phase2
if "%choice%"=="3" goto phase3
if "%choice%"=="4" goto all_phases
if "%choice%"=="5" goto generate_report
if "%choice%"=="6" goto check_all
if "%choice%"=="0" goto exit_script
echo [ERROR] 无效选择，请重新选择
goto main_menu

:phase1
echo.
echo [INFO] 开始执行第一阶段: 高优先级项目
echo ========================================
for %%p in (%phase1_projects%) do (
    call :process_project "%%p"
)
echo [SUCCESS] 第一阶段完成
goto main_menu

:phase2
echo.
echo [INFO] 开始执行第二阶段: 中优先级项目
echo ========================================
for %%p in (%phase2_projects%) do (
    call :process_project "%%p"
)
echo [SUCCESS] 第二阶段完成
goto main_menu

:phase3
echo.
echo [INFO] 开始执行第三阶段: 低优先级项目
echo ========================================
for %%p in (%phase3_projects%) do (
    call :process_project "%%p"
)
echo [SUCCESS] 第三阶段完成
goto main_menu

:all_phases
echo.
echo [INFO] 开始执行全部阶段完善
echo ========================================
echo 执行第一阶段...
for %%p in (%phase1_projects%) do (
    call :process_project "%%p"
)
echo.
echo 执行第二阶段...
for %%p in (%phase2_projects%) do (
    call :process_project "%%p"
)
echo.
echo 执行第三阶段...
for %%p in (%phase3_projects%) do (
    call :process_project "%%p"
)
echo [SUCCESS] 全部阶段完成
goto main_menu

:check_all
echo.
echo [INFO] 检查所有项目状态...
echo ========================================
echo 第一阶段项目:
for %%p in (%phase1_projects%) do (
    call :check_project_status "%%p"
)
echo.
echo 第二阶段项目:
for %%p in (%phase2_projects%) do (
    call :check_project_status "%%p"
)
echo.
echo 第三阶段项目:
for %%p in (%phase3_projects%) do (
    call :check_project_status "%%p"
)
echo ========================================
goto main_menu

:generate_report
echo.
echo [INFO] 生成项目状态报告...
set "timestamp=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
set "timestamp=%timestamp: =0%"
set "report_file=server\项目状态报告_%timestamp%.md"

echo # DL引擎微服务项目状态报告 > "%report_file%"
echo. >> "%report_file%"
echo 生成时间: %date% %time% >> "%report_file%"
echo. >> "%report_file%"
echo ## 项目状态概览 >> "%report_file%"
echo. >> "%report_file%"
echo ### 第一阶段项目 (高优先级) >> "%report_file%"

for %%p in (%phase1_projects%) do (
    if exist "server\%%p\package.json" (
        echo - ✅ %%p - 状态良好 >> "%report_file%"
    ) else (
        echo - ❌ %%p - 需要修复 >> "%report_file%"
    )
)

echo. >> "%report_file%"
echo ### 第二阶段项目 (中优先级) >> "%report_file%"

for %%p in (%phase2_projects%) do (
    if exist "server\%%p\package.json" (
        echo - ✅ %%p - 状态良好 >> "%report_file%"
    ) else (
        echo - ❌ %%p - 需要修复 >> "%report_file%"
    )
)

echo. >> "%report_file%"
echo ### 第三阶段项目 (低优先级) >> "%report_file%"

for %%p in (%phase3_projects%) do (
    if exist "server\%%p\package.json" (
        echo - ✅ %%p - 状态良好 >> "%report_file%"
    ) else (
        echo - ❌ %%p - 需要修复 >> "%report_file%"
    )
)

echo [SUCCESS] 状态报告已生成: %report_file%
goto main_menu

:process_project
set "project_name=%~1"
set "project_path=server\%project_name%"

echo [INFO] 处理项目: %project_name%

REM 检查项目目录是否存在
if not exist "%project_path%" (
    echo [ERROR] 项目目录不存在: %project_path%
    goto :eof
)

REM 检查package.json是否存在
if not exist "%project_path%\package.json" (
    echo [WARNING] 缺少 package.json: %project_name%
    goto :eof
)

REM 进入项目目录
cd /d "%project_path%"

REM 安装依赖
echo [INFO] 安装依赖...
npm install --legacy-peer-deps >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] 依赖安装完成: %project_name%
) else (
    echo [WARNING] 依赖安装失败: %project_name%
)

REM 测试构建
echo [INFO] 测试构建...
npm run build >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] 构建测试通过: %project_name%
) else (
    echo [WARNING] 构建测试失败: %project_name%
)

REM 返回原目录
cd /d "%~dp0\.."
echo ----------------------------------------
goto :eof

:check_project_status
set "project_name=%~1"
set "project_path=server\%project_name%"

if exist "%project_path%\package.json" (
    if exist "%project_path%\tsconfig.json" (
        echo [SUCCESS] %project_name% - 状态良好
    ) else (
        echo [WARNING] %project_name% - 缺少 tsconfig.json
    )
) else (
    echo [ERROR] %project_name% - 缺少 package.json
)
goto :eof

:exit_script
echo.
echo [INFO] 退出脚本
echo 感谢使用DL引擎项目完善工具！
pause
exit /b 0
