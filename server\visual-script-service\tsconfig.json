{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "esModuleInterop": true, "resolveJsonModule": true, "paths": {"@/*": ["src/*"], "@shared/*": ["../shared/*"], "@engine/*": ["../../editor/src/libs/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*spec.ts"]}