/**
 * 模型管理控制器
 * 提供AI模型的CRUD操作和管理功能
 */

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  UseInterceptors,
  UploadedFile,
  Logger
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiConsumes } from '@nestjs/swagger';
import { AIAlgorithmService, ModelConfig, TrainingJob, ModelEvaluation } from '../services/ai-algorithm.service';
import { CreateModelDto, UpdateModelDto, StartTrainingDto } from '../dto/model.dto';

@ApiTags('模型管理')
@Controller('api/v1/models')
export class ModelManagementController {
  private readonly logger = new Logger(ModelManagementController.name);

  constructor(private readonly aiService: AIAlgorithmService) {}

  /**
   * 创建新模型
   */
  @Post()
  @ApiOperation({ summary: '创建新的AI模型' })
  @ApiResponse({ status: 201, description: '模型创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async createModel(@Body() createModelDto: CreateModelDto): Promise<ModelConfig> {
    try {
      this.logger.log(`创建模型请求: ${createModelDto.name}`);
      
      const model = await this.aiService.createModel({
        name: createModelDto.name,
        type: createModelDto.type,
        version: createModelDto.version || '1.0.0',
        description: createModelDto.description || '',
        architecture: createModelDto.architecture,
        hyperparameters: createModelDto.hyperparameters || {},
        trainingConfig: createModelDto.trainingConfig
      });

      this.logger.log(`模型创建成功: ${model.id}`);
      return model;

    } catch (error) {
      this.logger.error(`创建模型失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 获取模型列表
   */
  @Get()
  @ApiOperation({ summary: '获取所有模型列表' })
  @ApiQuery({ name: 'type', required: false, description: '模型类型过滤' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getModels(@Query('type') type?: string): Promise<ModelConfig[]> {
    try {
      let models = this.aiService.getModels();
      
      if (type) {
        models = models.filter(model => model.type === type);
      }

      this.logger.log(`获取模型列表: ${models.length} 个模型`);
      return models;

    } catch (error) {
      this.logger.error(`获取模型列表失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取单个模型详情
   */
  @Get(':id')
  @ApiOperation({ summary: '获取模型详情' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '模型不存在' })
  async getModel(@Param('id') id: string): Promise<ModelConfig> {
    try {
      const models = this.aiService.getModels();
      const model = models.find(m => m.id === id);

      if (!model) {
        throw new HttpException('模型不存在', HttpStatus.NOT_FOUND);
      }

      this.logger.log(`获取模型详情: ${id}`);
      return model;

    } catch (error) {
      this.logger.error(`获取模型详情失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 更新模型
   */
  @Put(':id')
  @ApiOperation({ summary: '更新模型配置' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '模型不存在' })
  async updateModel(
    @Param('id') id: string,
    @Body() updateModelDto: UpdateModelDto
  ): Promise<ModelConfig> {
    try {
      this.logger.log(`更新模型请求: ${id}`);
      
      const updatedModel = await this.aiService.updateModel(id, updateModelDto);
      
      this.logger.log(`模型更新成功: ${id}`);
      return updatedModel;

    } catch (error) {
      this.logger.error(`更新模型失败: ${error.message}`);
      if (error.message.includes('不存在')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 开始训练
   */
  @Post(':id/training')
  @ApiOperation({ summary: '开始模型训练' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiResponse({ status: 201, description: '训练开始成功' })
  @ApiResponse({ status: 404, description: '模型不存在' })
  async startTraining(
    @Param('id') id: string,
    @Body() startTrainingDto?: StartTrainingDto
  ): Promise<TrainingJob> {
    try {
      this.logger.log(`开始训练请求: ${id}`);
      
      const trainingJob = await this.aiService.startTraining(id, startTrainingDto?.trainingConfig);
      
      this.logger.log(`训练开始成功: ${trainingJob.id}`);
      return trainingJob;

    } catch (error) {
      this.logger.error(`开始训练失败: ${error.message}`);
      if (error.message.includes('不存在')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 获取训练任务列表
   */
  @Get(':id/training')
  @ApiOperation({ summary: '获取模型的训练任务列表' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getTrainingJobs(@Param('id') id: string): Promise<TrainingJob[]> {
    try {
      const jobs = this.aiService.getTrainingJobs(id);
      
      this.logger.log(`获取训练任务列表: ${id}, ${jobs.length} 个任务`);
      return jobs;

    } catch (error) {
      this.logger.error(`获取训练任务列表失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 停止训练
   */
  @Delete(':id/training/:jobId')
  @ApiOperation({ summary: '停止训练任务' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiParam({ name: 'jobId', description: '训练任务ID' })
  @ApiResponse({ status: 200, description: '停止成功' })
  @ApiResponse({ status: 404, description: '训练任务不存在' })
  async stopTraining(
    @Param('id') id: string,
    @Param('jobId') jobId: string
  ): Promise<{ message: string }> {
    try {
      this.logger.log(`停止训练请求: ${jobId}`);
      
      await this.aiService.stopTraining(jobId);
      
      this.logger.log(`训练停止成功: ${jobId}`);
      return { message: '训练已停止' };

    } catch (error) {
      this.logger.error(`停止训练失败: ${error.message}`);
      if (error.message.includes('不存在')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 模型评估
   */
  @Post(':id/evaluation')
  @ApiOperation({ summary: '评估模型性能' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiResponse({ status: 201, description: '评估完成' })
  @ApiResponse({ status: 404, description: '模型不存在' })
  async evaluateModel(
    @Param('id') id: string,
    @Body() body: { testDataset: string }
  ): Promise<ModelEvaluation> {
    try {
      this.logger.log(`模型评估请求: ${id}`);
      
      const evaluation = await this.aiService.evaluateModel(id, body.testDataset);
      
      this.logger.log(`模型评估完成: ${id}`);
      return evaluation;

    } catch (error) {
      this.logger.error(`模型评估失败: ${error.message}`);
      if (error.message.includes('不存在')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 上传模型文件
   */
  @Post(':id/upload')
  @ApiOperation({ summary: '上传模型文件' })
  @ApiConsumes('multipart/form-data')
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiResponse({ status: 201, description: '上传成功' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadModelFile(
    @Param('id') id: string,
    @UploadedFile() file: Express.Multer.File
  ): Promise<{ message: string; filename: string }> {
    try {
      this.logger.log(`模型文件上传请求: ${id}, 文件: ${file.originalname}`);
      
      // 这里应该实现实际的文件保存逻辑
      // 暂时返回成功响应
      
      this.logger.log(`模型文件上传成功: ${id}`);
      return {
        message: '文件上传成功',
        filename: file.originalname
      };

    } catch (error) {
      this.logger.error(`模型文件上传失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }
}
