#!/bin/bash

echo "🔧 修复Voice Service依赖问题..."
echo

# 检查Node.js版本
echo "📋 检查Node.js版本..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装或不在PATH中"
    exit 1
fi
node --version

# 检查npm版本
echo "📋 检查npm版本..."
if ! command -v npm &> /dev/null; then
    echo "❌ npm未安装或不在PATH中"
    exit 1
fi
npm --version

# 清理现有的node_modules和package-lock.json
echo "🗑️  清理现有依赖..."
if [ -d "node_modules" ]; then
    rm -rf node_modules
    echo "✅ 已删除node_modules目录"
fi

if [ -f "package-lock.json" ]; then
    rm package-lock.json
    echo "✅ 已删除package-lock.json文件"
fi

# 清理npm缓存
echo "🗑️  清理npm缓存..."
npm cache clean --force
if [ $? -eq 0 ]; then
    echo "✅ npm缓存清理成功"
else
    echo "⚠️  npm缓存清理失败，继续安装..."
fi

# 设置npm镜像源（可选，如果网络较慢）
echo "🌐 设置npm镜像源..."
npm config set registry https://registry.npmmirror.com/
echo "✅ 已设置为淘宝镜像源"

# 安装依赖
echo "📦 安装项目依赖..."
npm install --legacy-peer-deps

if [ $? -eq 0 ]; then
    echo "✅ 依赖安装成功！"
    
    # 验证关键依赖
    echo "🔍 验证关键依赖..."
    
    if npm list @nestjs/core &> /dev/null; then
        echo "✅ @nestjs/core 安装成功"
    else
        echo "❌ @nestjs/core 安装失败"
    fi
    
    if npm list @nestjs/event-emitter &> /dev/null; then
        echo "✅ @nestjs/event-emitter 安装成功"
    else
        echo "❌ @nestjs/event-emitter 安装失败"
    fi
    
    if npm list microsoft-cognitiveservices-speech-sdk &> /dev/null; then
        echo "✅ microsoft-cognitiveservices-speech-sdk 安装成功"
    else
        echo "❌ microsoft-cognitiveservices-speech-sdk 安装失败"
    fi
    
    if npm list fluent-ffmpeg &> /dev/null; then
        echo "✅ fluent-ffmpeg 安装成功"
    else
        echo "❌ fluent-ffmpeg 安装失败"
    fi
    
    echo
    echo "🎉 依赖修复完成！"
    echo
    echo "📝 下一步操作："
    echo "1. 配置环境变量: cp .env.example .env"
    echo "2. 编辑 .env 文件配置语音服务API密钥"
    echo "3. 启动Redis: docker-compose up -d redis"
    echo "4. 启动服务: npm run start:dev"
    echo
    
else
    echo "❌ 依赖安装失败！"
    echo
    echo "🔧 可能的解决方案："
    echo "1. 检查网络连接"
    echo "2. 尝试使用不同的npm镜像源"
    echo "3. 检查Node.js版本是否兼容（推荐18+）"
    echo "4. 手动安装有问题的依赖包"
    echo
    echo "📋 手动安装命令："
    echo "npm install @nestjs/event-emitter@^2.0.0 --legacy-peer-deps"
    echo "npm install @nestjs/bull@^0.6.3 --legacy-peer-deps"
    echo "npm install @google-cloud/speech@^5.5.0 --legacy-peer-deps"
    echo "npm install @google-cloud/text-to-speech@^4.2.0 --legacy-peer-deps"
    echo
fi
