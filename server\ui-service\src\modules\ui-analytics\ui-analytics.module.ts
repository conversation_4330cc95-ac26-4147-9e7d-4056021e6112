import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UIAnalyticsController } from './ui-analytics.controller';
import { UIAnalyticsService } from './ui-analytics.service';
import { UIAnalytics, UIAnalyticsSchema } from './schemas/ui-analytics.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UIAnalytics.name, schema: UIAnalyticsSchema },
    ]),
  ],
  controllers: [UIAnalyticsController],
  providers: [UIAnalyticsService],
  exports: [UIAnalyticsService],
})
export class UIAnalyticsModule {}
