import { Controller, Get, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { UIVersionService } from './ui-version.service';

@ApiTags('ui-version')
@Controller('ui-version')
export class UIVersionController {
  constructor(private readonly versionService: UIVersionService) {}

  @Get(':resourceId/history')
  @ApiOperation({ summary: '获取资源版本历史' })
  @ApiParam({ name: 'resourceId', description: '资源ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getVersionHistory(
    @Param('resourceId') resourceId: string,
    @Query('limit') limit?: number,
  ) {
    return this.versionService.getVersionHistory(
      new Types.ObjectId(resourceId),
      limit,
    );
  }

  @Get(':resourceId/latest')
  @ApiOperation({ summary: '获取最新版本' })
  @ApiParam({ name: 'resourceId', description: '资源ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getLatestVersion(@Param('resourceId') resourceId: string) {
    return this.versionService.getLatestVersion(new Types.ObjectId(resourceId));
  }

  @Get(':resourceId/:version')
  @ApiOperation({ summary: '获取指定版本' })
  @ApiParam({ name: 'resourceId', description: '资源ID' })
  @ApiParam({ name: 'version', description: '版本号' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getVersion(
    @Param('resourceId') resourceId: string,
    @Param('version') version: string,
  ) {
    return this.versionService.getVersion(
      new Types.ObjectId(resourceId),
      version,
    );
  }
}
