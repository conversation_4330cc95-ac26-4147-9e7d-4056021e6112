import { IsString, <PERSON>Enum, IsOptional, IsObject, IsBoolean, IsArray, IsMongoId, MaxLength, IsNumber, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { ComponentType, ComponentStatus, ComponentProperty, ComponentEvent, ComponentStyle } from '../schemas/ui-component.schema';

export class CreateUIComponentDto {
  @ApiProperty({ description: '组件名称', maxLength: 100 })
  @IsString()
  @MaxLength(100)
  name: string;

  @ApiPropertyOptional({ description: '组件描述', maxLength: 500 })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiProperty({ description: '组件类型', enum: ComponentType })
  @IsEnum(ComponentType)
  type: ComponentType;

  @ApiProperty({ description: '组件版本' })
  @IsString()
  version: string;

  @ApiPropertyOptional({ description: '标签', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({ description: 'HTML模板' })
  @IsString()
  template: string;

  @ApiPropertyOptional({ description: 'JavaScript代码' })
  @IsOptional()
  @IsString()
  script?: string;

  @ApiPropertyOptional({ description: 'CSS样式' })
  @IsOptional()
  @IsString()
  style?: string;

  @ApiPropertyOptional({ description: '组件属性', type: [Object] })
  @IsOptional()
  @IsArray()
  properties?: ComponentProperty[];

  @ApiPropertyOptional({ description: '组件事件', type: [Object] })
  @IsOptional()
  @IsArray()
  events?: ComponentEvent[];

  @ApiPropertyOptional({ description: '组件样式', type: [Object] })
  @IsOptional()
  @IsArray()
  styles?: ComponentStyle[];

  @ApiPropertyOptional({ description: '元数据', type: 'object' })
  @IsOptional()
  @IsObject()
  metadata?: any;

  @ApiPropertyOptional({ description: '配置', type: 'object' })
  @IsOptional()
  @IsObject()
  config?: any;

  @ApiPropertyOptional({ description: '组织ID' })
  @IsOptional()
  @IsMongoId()
  organizationId?: string;

  @ApiPropertyOptional({ description: '是否公开', default: true })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;
}

export class UpdateUIComponentDto {
  @ApiPropertyOptional({ description: '组件名称', maxLength: 100 })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  name?: string;

  @ApiPropertyOptional({ description: '组件描述', maxLength: 500 })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiPropertyOptional({ description: '组件状态', enum: ComponentStatus })
  @IsOptional()
  @IsEnum(ComponentStatus)
  status?: ComponentStatus;

  @ApiPropertyOptional({ description: '组件版本' })
  @IsOptional()
  @IsString()
  version?: string;

  @ApiPropertyOptional({ description: '标签', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: 'HTML模板' })
  @IsOptional()
  @IsString()
  template?: string;

  @ApiPropertyOptional({ description: 'JavaScript代码' })
  @IsOptional()
  @IsString()
  script?: string;

  @ApiPropertyOptional({ description: 'CSS样式' })
  @IsOptional()
  @IsString()
  style?: string;

  @ApiPropertyOptional({ description: '组件属性', type: [Object] })
  @IsOptional()
  @IsArray()
  properties?: ComponentProperty[];

  @ApiPropertyOptional({ description: '组件事件', type: [Object] })
  @IsOptional()
  @IsArray()
  events?: ComponentEvent[];

  @ApiPropertyOptional({ description: '组件样式', type: [Object] })
  @IsOptional()
  @IsArray()
  styles?: ComponentStyle[];

  @ApiPropertyOptional({ description: '元数据', type: 'object' })
  @IsOptional()
  @IsObject()
  metadata?: any;

  @ApiPropertyOptional({ description: '配置', type: 'object' })
  @IsOptional()
  @IsObject()
  config?: any;

  @ApiPropertyOptional({ description: '是否公开' })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;

  @ApiPropertyOptional({ description: '是否激活' })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class QueryUIComponentDto {
  @ApiPropertyOptional({ description: '页码', minimum: 1, default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100, default: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({ description: '搜索关键词' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: '组件类型', enum: ComponentType })
  @IsOptional()
  @IsEnum(ComponentType)
  type?: ComponentType;

  @ApiPropertyOptional({ description: '组件状态', enum: ComponentStatus })
  @IsOptional()
  @IsEnum(ComponentStatus)
  status?: ComponentStatus;

  @ApiPropertyOptional({ description: '组织ID' })
  @IsOptional()
  @IsMongoId()
  organizationId?: string;

  @ApiPropertyOptional({ description: '标签' })
  @IsOptional()
  @IsString()
  tags?: string;

  @ApiPropertyOptional({ description: '是否公开' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  isPublic?: boolean;

  @ApiPropertyOptional({ description: '排序字段', default: 'createdAt' })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ description: '排序方向', enum: ['asc', 'desc'], default: 'desc' })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc';
}
